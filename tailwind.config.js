/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#135673', // Dark Teal
        secondary: '#6a92a1', // Muted Blue-Gray
        tertiary: '#bad9e4', // Light Blue
        accent: '#d5467e', // Soft Pink
        neutral: '#f8f9fa', // Off-White
        text: '#333333', // Dark Gray
        'text-light': '#6c757d', // Medium Gray
      },
      fontFamily: {
        sans: ['Nunito Sans', 'sans-serif'],
        heading: ['Nunito Sans', 'sans-serif'],
        body: ['Open Sans', 'sans-serif'],
      },
      container: {
        center: true,
        padding: {
          DEFAULT: '1rem',
          sm: '2rem',
          lg: '4rem',
          xl: '5rem',
        },
      },
      boxShadow: {
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      },
    },
  },
  plugins: [],
}
