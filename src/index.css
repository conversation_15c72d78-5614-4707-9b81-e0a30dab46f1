@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #135673; /* Teal from the logo */
  --secondary: #6a92a1;
  --tertiary: #bad9e4;
  --accent: #e94a7a; /* Pink from the logo */
  --neutral: #f8f9fa;
  --text: #333333;
  --text-light: #6c757d;
}

@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-neutral text-text font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-heading font-bold text-primary;
  }

  h1 {
    @apply text-4xl md:text-5xl leading-tight;
  }

  h2 {
    @apply text-3xl md:text-4xl leading-tight;
  }

  h3 {
    @apply text-2xl md:text-3xl leading-snug;
  }

  p {
    @apply mb-4 leading-relaxed;
  }

  a {
    @apply text-primary hover:text-accent transition-colors duration-200;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-accent text-white hover:bg-primary focus:ring-accent;
  }

  .btn-secondary {
    @apply btn border-2 border-primary text-primary hover:bg-primary/10 focus:ring-primary;
  }

  .card {
    @apply bg-white rounded-lg shadow-sm hover:shadow transition-shadow duration-200;
  }

  .section {
    @apply py-12 md:py-24;
  }
  
  /* Custom height classes for logo sizes */
  .h-15 {
    height: 3.75rem; /* 150% of 2.5rem (h-10) */
  }

  /* Custom height class for header (h-18) */
  .h-18 {
    height: 4.5rem; /* 18 * 0.25rem */
  }

  /* 225% of original h-10 size (2.5rem) */
  .h-22 {
    height: 5.625rem;
  }

  /* Custom height class for larger logo (h-40) */
  .h-40 {
    height: 10rem;
  }

  /* Responsive utilities */
  @media (max-width: 640px) {
    .container {
      @apply px-3;
    }
  }

  /* Mobile menu improvements */
  .mobile-menu-backdrop {
    backdrop-filter: blur(4px);
    background-color: rgba(255, 255, 255, 0.95);
  }

  /* Ensure mobile menu covers full viewport */
  .mobile-menu-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    min-height: 100vh !important;
    z-index: 9999 !important;
    /* Prevent horizontal scrollbars */
    max-width: 100vw !important;
    overflow-x: hidden !important;
    /* Ensure proper transform context */
    transform-style: preserve-3d;
    will-change: transform;
  }

  /* Header improvements for better contrast */
  .header-solid-bg {
    background-color: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  /* Ensure header is sticky on all screen sizes */
  header {
    position: sticky !important;
    top: 0 !important;
    z-index: 50 !important;
    /* Prevent layout shifts during scrolling - modified to not interfere with mobile menu */
    contain: layout style;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Logo sizing constraints */
  .logo-container img {
    max-height: 100%;
    width: auto;
    object-fit: contain;
  }

  /* Responsive breakpoint adjustments */
  @media (min-width: 768px) and (max-width: 1023px) {
    .header-tablet {
      height: 4.5rem; /* h-18 */
    }
  }

  /* Mobile header adjustments */
  @media (max-width: 767px) {
    header {
      height: 3.5rem !important; /* h-14 */
      /* Additional mobile optimizations */
      -webkit-transform: translateZ(0);
      transform: translateZ(0);
      -webkit-backface-visibility: hidden;
      backface-visibility: hidden;
    }
  }

  /* Prevent content jump by ensuring consistent spacing */
  body {
    scroll-behavior: smooth;
  }

  /* Optimize scroll performance on mobile */
  @media (max-width: 767px) {
    * {
      -webkit-overflow-scrolling: touch;
    }

    /* Prevent horizontal scrollbars when mobile menu is open */
    body.mobile-menu-open {
      overflow-x: hidden !important;
      position: fixed !important;
      width: 100% !important;
    }
  }

  /* Additional mobile menu optimizations */
  .mobile-menu-overlay {
    /* Ensure smooth slide animation */
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  }

  /* Prevent any potential horizontal overflow */
  html, body {
    max-width: 100vw;
    overflow-x: hidden;
  }

  /* Keeping float animations for other components that may use them */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes float-delayed {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-15px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-float-delayed {
    animation: float-delayed 4s ease-in-out infinite;
    animation-delay: 1s;
  }
}

/* Accessibility styles */
.focus-visible:focus {
  @apply outline-none ring-2 ring-accent ring-offset-2;
}

.skip-link {
  @apply sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:z-50 focus:bg-white focus:p-4 focus:text-accent;
}
