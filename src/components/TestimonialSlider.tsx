import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    text: "The Amara Nursing team provided exceptional care for my mother after her hospital discharge. Their expertise and compassion made a difficult time much easier for our family.",
    author: "<PERSON>, Seattle"
  },
  {
    id: 2,
    text: "Professional, caring, and reliable. The nurses at Amara went above and beyond to ensure my father received the best possible care at home. I couldn't be more grateful.",
    author: "<PERSON>, Lynnwood"
  },
  {
    id: 3,
    text: "The Adult Family Home is truly a home away from home. The staff treats residents with dignity and respect, and the medical care is outstanding.",
    author: "<PERSON>, <PERSON>"
  },
  {
    id: 4,
    text: "Amara Nursing helped us navigate a complex medical situation with grace and expertise. Their 24/7 availability gave us peace of mind during a challenging time.",
    author: "<PERSON>, Bellevue"
  }
];

const TestimonialSlider = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-advance testimonials every 6 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 6000);

    return () => clearInterval(timer);
  }, []);

  const goToPrevious = () => {
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
  };

  const goToNext = () => {
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
  };

  return (
    <div className="relative max-w-4xl mx-auto">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className="text-center"
        >
          <div className="text-6xl text-accent opacity-20 font-serif">"</div>
          <p className="text-xl md:text-2xl font-light italic text-text mb-8 max-w-3xl mx-auto">
            {testimonials[currentIndex].text}
          </p>
          <footer className="font-medium text-text-light">
            — {testimonials[currentIndex].author}
          </footer>
        </motion.div>
      </AnimatePresence>

      {/* Navigation Buttons */}
      <div className="flex justify-center items-center mt-8 space-x-4">
        <button
          onClick={goToPrevious}
          className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
          aria-label="Previous testimonial"
        >
          <ChevronLeft className="w-5 h-5 text-primary" />
        </button>

        {/* Dots Indicator */}
        <div className="flex space-x-2">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentIndex ? 'bg-primary' : 'bg-primary/30'
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>

        <button
          onClick={goToNext}
          className="p-2 rounded-full bg-primary/10 hover:bg-primary/20 transition-colors"
          aria-label="Next testimonial"
        >
          <ChevronRight className="w-5 h-5 text-primary" />
        </button>
      </div>
    </div>
  );
};

export default TestimonialSlider;
