import { Outlet } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';
import ScrollToTop from './ScrollToTop';
import TopBar from './TopBar';
import { Helmet } from 'react-helmet-async';

const Layout = () => {
  return (
    <>
      <Helmet>
        <title>Amara Nursing LLC | Compassionate Healthcare</title>
        <meta name="description" content="Professional in-home nursing care and a dedicated Adult Family Home in Lynnwood, WA." />
      </Helmet>
      <div className="flex flex-col min-h-screen">
        <ScrollToTop />
        <a href="#main" className="skip-link">
          Skip to main content
        </a>
        <TopBar />
        <Header />
        <main id="main" className="flex-grow">
          <Outlet />
        </main>
        <Footer />
      </div>
    </>
  );
};

export default Layout;
