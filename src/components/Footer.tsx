import { Link } from 'react-router-dom';
import Logo from './Logo';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-primary text-white">
      <div className="container mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 items-center">

          {/* Left: Logo and Copyright */}
          <div className="text-center lg:text-left">
            <Logo className="h-10 sm:h-12 w-auto brightness-0 invert mx-auto lg:mx-0 mb-3" />
            <p className="text-sm text-tertiary/80">
              &copy; {currentYear} Amara Nursing LLC. All Rights Reserved.
            </p>
          </div>

          {/* Center: Navigation Links */}
          <div className="text-center">
            <nav>
              <ul className="flex flex-wrap justify-center gap-x-6 gap-y-2 text-sm">
                <li>
                  <Link
                    to="/"
                    className="text-tertiary/90 hover:text-white transition-colors duration-300"
                  >
                    Home
                  </Link>
                </li>
                <li>
                  <Link
                    to="/services"
                    className="text-tertiary/90 hover:text-white transition-colors duration-300"
                  >
                    Services
                  </Link>
                </li>
                <li>
                  <Link
                    to="/afh"
                    className="text-tertiary/90 hover:text-white transition-colors duration-300"
                  >
                    Adult Family Home
                  </Link>
                </li>
                <li>
                  <Link
                    to="/about"
                    className="text-tertiary/90 hover:text-white transition-colors duration-300"
                  >
                    About Us
                  </Link>
                </li>
                <li>
                  <Link
                    to="/contact"
                    className="text-tertiary/90 hover:text-white transition-colors duration-300"
                  >
                    Contact
                  </Link>
                </li>
              </ul>
            </nav>
          </div>

          {/* Right: Essential Contact Details */}
          <div className="text-center lg:text-right">
            <div className="space-y-2 text-sm">
              <div>
                <span className="text-tertiary/80">Adult Family Home: </span>
                <a
                  href="tel:+18174719954"
                  className="text-white hover:text-accent transition-colors duration-300 font-medium"
                >
                  (*************
                </a>
              </div>
              <div>
                <span className="text-tertiary/80">Administrative Office: </span>
                <a
                  href="tel:+18176001793"
                  className="text-white hover:text-accent transition-colors duration-300 font-medium"
                >
                  (*************
                </a>
              </div>
            </div>
          </div>

        </div>
      </div>
    </footer>
  );
};

export default Footer;
