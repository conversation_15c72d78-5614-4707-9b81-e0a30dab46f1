import { FC } from 'react';

interface LogoProps {
  className?: string;
  responsive?: boolean;
}

const Logo: FC<LogoProps> = ({
  className = 'h-16 w-auto',
  responsive = false
}) => {
  // Always use the full logo
  // Apply responsive classes if responsive prop is true, otherwise use provided className
  // Reduced logo sizes by approximately 30% for larger screens
  const finalClasses = responsive
    ? 'h-8 sm:h-9 md:h-10 lg:h-11 w-auto max-w-none object-contain'
    : `${className} object-contain`;

  return (
    <img
      src="/logos/logo0.png"
      alt="Amara Nursing LLC"
      className={finalClasses}
      style={{ imageRendering: 'crisp-edges' }}
    />
  );
};

export default Logo;
