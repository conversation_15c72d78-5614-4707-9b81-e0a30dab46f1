import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useState } from 'react';
import { getBrowserInfo, getLocationData } from '../utils/browserInfo';

const contactFormSchema = z.object({
  fullName: z.string().min(2, { message: 'Full name is required' }),
  email: z.string().email({ message: 'Valid email is required' }),
  phone: z.string().optional(),
  subject: z.string().min(1, { message: 'Please select a subject' }),
  message: z.string().min(10, { message: 'Message should be at least 10 characters' }).max(500, { message: 'Message should not exceed 500 characters' }),
});

type ContactFormData = z.infer<typeof contactFormSchema>;

const subjects = [
  { value: 'general', label: 'General Inquiry' },
  { value: 'in-home', label: 'In-Home Care' },
  { value: 'afh', label: 'Adult Family Home Inquiry' },
  { value: 'service-area', label: 'Service Area Question' },
];

export default function ContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>({
    resolver: zodResolver(contactFormSchema),
  });

  const onSubmit = async (formData: ContactFormData) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Collect browser and location metadata
      const browserInfo = getBrowserInfo();
      const locationData = await getLocationData();

      // Combine form data with metadata
      const submissionData = {
        ...formData,
        browserInfo,
        locationData,
      };

      const response = await fetch('/.netlify/functions/contact-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to send message');
      }

      if (result.success) {
        setIsSubmitted(true);
        reset();
      } else {
        throw new Error(result.error || 'Failed to send message');
      }
    } catch (err) {
      console.error('Contact form error:', err);
      setError(
        err instanceof Error
          ? err.message
          : 'There was a problem submitting your form. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSubmitted) {
    return (
      <div className="bg-tertiary/20 p-8 rounded-lg text-center">
        <h3 className="text-2xl font-semibold text-primary mb-4">Thank You!</h3>
        <p className="mb-6">Your message has been sent successfully. We'll get back to you as soon as possible.</p>
        <button 
          onClick={() => setIsSubmitted(false)}
          className="btn-secondary"
        >
          Send Another Message
        </button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" noValidate>
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          {error}
        </div>
      )}
      
      <div>
        <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
          Full Name <span className="text-accent">*</span>
        </label>
        <input
          id="fullName"
          type="text"
          className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-accent ${errors.fullName ? 'border-red-500' : 'border-gray-300'}`}
          {...register('fullName')}
          aria-invalid={errors.fullName ? 'true' : 'false'}
        />
        {errors.fullName && (
          <p className="mt-1 text-sm text-red-600">{errors.fullName.message}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
          Email Address <span className="text-accent">*</span>
        </label>
        <input
          id="email"
          type="email"
          className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-accent ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
          {...register('email')}
          aria-invalid={errors.email ? 'true' : 'false'}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
          Phone Number (Optional)
        </label>
        <input
          id="phone"
          type="tel"
          className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent"
          {...register('phone')}
        />
      </div>
      
      <div>
        <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-1">
          Subject <span className="text-accent">*</span>
        </label>
        <select
          id="subject"
          className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-accent ${errors.subject ? 'border-red-500' : 'border-gray-300'}`}
          {...register('subject')}
          aria-invalid={errors.subject ? 'true' : 'false'}
        >
          <option value="">Please select a subject</option>
          {subjects.map((subject) => (
            <option key={subject.value} value={subject.value}>
              {subject.label}
            </option>
          ))}
        </select>
        {errors.subject && (
          <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
        )}
      </div>
      
      <div>
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
          Your Message <span className="text-accent">*</span>
        </label>
        <textarea
          id="message"
          rows={5}
          className={`w-full px-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-accent ${errors.message ? 'border-red-500' : 'border-gray-300'}`}
          {...register('message')}
          aria-invalid={errors.message ? 'true' : 'false'}
        ></textarea>
        {errors.message && (
          <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
        )}
      </div>

      <div>
        <button
          type="submit"
          className="btn-primary w-full"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Sending...' : 'Send Message'}
        </button>
      </div>
    </form>
  );
}
