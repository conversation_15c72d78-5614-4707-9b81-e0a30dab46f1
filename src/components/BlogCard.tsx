import { FC } from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Calendar } from 'lucide-react';

interface BlogCardProps {
  id: number;
  title: string;
  slug: string;
  date: string;
  excerpt: string;
  thumbnailDesc: string;
}

const BlogCard: FC<BlogCardProps> = ({ title, slug, date, excerpt, thumbnailDesc }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden h-full flex flex-col">
      {/* Placeholder image */}
      <div className="bg-primary/10 h-48 relative">
        <div className="absolute inset-0 flex items-center justify-center text-primary/30 text-sm p-4 text-center">
          {thumbnailDesc}
        </div>
      </div>
      
      <div className="p-6 flex-grow flex flex-col">
        <div className="flex items-center text-text-light text-sm mb-3">
          <Calendar size={14} className="mr-1" />
          <span>{date}</span>
        </div>
        
        <h3 className="text-xl font-bold mb-3 text-primary line-clamp-2">
          {title}
        </h3>
        
        <p className="text-text-light mb-4 line-clamp-3 flex-grow">
          {excerpt}
        </p>
        
        <Link 
          to={`/blog/${slug}`} 
          className="inline-flex items-center text-accent font-semibold text-sm hover:underline mt-auto"
        >
          Read More <ArrowRight size={16} className="ml-1" />
        </Link>
      </div>
    </div>
  );
};

export default BlogCard;
