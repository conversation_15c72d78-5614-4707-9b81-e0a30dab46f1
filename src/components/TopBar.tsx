import { useState } from 'react';
import { X, Mail, Phone } from 'lucide-react';
import { useScrolled } from '../hooks/useScrolled';

const TopBar = () => {
  const [isVisible, setIsVisible] = useState(true);
  const isScrolled = useScrolled(10);

  // Use opacity transition instead of removing from DOM to prevent layout shift
  if (!isVisible) {
    return (
      <div className="h-8 sm:h-10 opacity-0 pointer-events-none transition-opacity duration-300" aria-hidden="true">
        {/* Invisible placeholder to maintain layout */}
      </div>
    );
  }

  return (
    <div className={`h-8 sm:h-10 flex items-center justify-between px-2 sm:px-4 text-xs sm:text-sm transition-all duration-300 ${isScrolled ? 'bg-primary' : 'bg-transparent'}`}>
      <div className="text-white hidden sm:block">
        We take care of you
      </div>
      <div className="text-white sm:hidden text-xs">
        We care for you
      </div>
      <div className="flex items-center space-x-2 sm:space-x-6">
        <div className="flex items-center space-x-1 sm:space-x-2">
          <Mail size={12} className="text-white sm:hidden" />
          <Mail size={14} className="text-white hidden sm:block" />
          <a
            href="mailto:<EMAIL>"
            className="text-white font-bold hover:text-accent transition-colors text-xs sm:text-sm"
          >
            <span className="hidden sm:inline"><EMAIL></span>
            <span className="sm:hidden">Email</span>
          </a>
        </div>
        <div className="flex items-center space-x-1 sm:space-x-2">
          <Phone size={12} className="text-white sm:hidden" />
          <Phone size={14} className="text-white hidden sm:block" />
          <a
            href="tel:+18174719954"
            className="text-white font-bold hover:text-accent transition-colors text-xs sm:text-sm"
          >
            <span className="hidden sm:inline">(*************</span>
            <span className="sm:hidden">Call</span>
          </a>
        </div>
        <button
          onClick={() => setIsVisible(false)}
          className="text-white hover:text-accent transition-colors p-1"
          aria-label="Close top bar"
        >
          <X size={12} className="sm:hidden" />
          <X size={14} className="hidden sm:block" />
        </button>
      </div>
    </div>
  );
};

export default TopBar;
