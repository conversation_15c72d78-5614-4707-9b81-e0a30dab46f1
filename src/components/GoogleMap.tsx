import { FC } from 'react';

interface GoogleMapProps {
  address: string;
  className?: string;
  height?: string;
}

const GoogleMap: FC<GoogleMapProps> = ({
  address,
  className = 'w-full h-full rounded-lg overflow-hidden',
  height = '400px'
}) => {
  // Encode the address for the URL
  const encodedAddress = encodeURIComponent(address);

  // Use satellite view as default with Google Maps embed
  const mapSrc = `https://www.google.com/maps/embed/v1/place?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'AIzaSyA0s1a7phLN0iaD6-UE7m4qP-z21pH0eSc'}&q=${encodedAddress}&maptype=satellite`;

  return (
    <div className={className} style={{ height }}>
      <iframe
        title="Google Maps Location"
        width="100%"
        height="100%"
        style={{ border: 0 }}
        loading="lazy"
        allowFullScreen
        referrerPolicy="no-referrer-when-downgrade"
        src={mapSrc}
      ></iframe>
    </div>
  );
};

export default GoogleMap;
