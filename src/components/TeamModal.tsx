import { FC, useEffect } from 'react';
import { X, Users } from 'lucide-react';

interface Leader {
  id: string;
  name: string;
  title: string;
  image: string;
  bio: string;
  experience: string;
  education: string;
  certifications: string[];
}

interface TeamModalProps {
  isOpen: boolean;
  onClose: () => void;
  leader: Leader | null;
}

const TeamModal: FC<TeamModalProps> = ({ isOpen, onClose, leader }) => {
  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen || !leader) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={onClose}
      />

      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 z-10 p-2 bg-white/90 hover:bg-white rounded-full shadow-lg transition-all duration-200 hover:scale-110"
          aria-label="Close modal"
        >
          <X size={20} className="text-gray-700" />
        </button>

        {/* Split Layout Container */}
        <div className="flex flex-col md:flex-row min-h-full">
          {/* Left Side - Image Section */}
          <div className="w-full md:w-2/5 relative">
            {leader.image && !leader.image.includes('placeholder') ? (
              <div className="relative h-[50vh] md:h-[90vh] md:sticky md:top-0">
                <img
                  src={leader.image}
                  alt={leader.name}
                  className="w-full h-full object-cover object-top"
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    e.currentTarget.style.display = 'none';
                    const placeholder = e.currentTarget.parentElement?.querySelector('.placeholder-content');
                    if (placeholder) {
                      (placeholder as HTMLElement).style.display = 'flex';
                    }
                  }}
                />
                {/* Subtle overlay for depth and text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none"></div>
              </div>
            ) : (
              /* Fallback Placeholder */
              <div className="placeholder-content h-[50vh] md:h-[90vh] bg-gradient-to-br from-primary/10 to-accent/10 flex items-center justify-center">
                <div className="text-center">
                  <Users className="w-16 h-16 text-primary/50 mx-auto mb-3" />
                  <p className="text-primary/70 text-sm font-medium">Team Member Photo</p>
                </div>
              </div>
            )}
          </div>

          {/* Right Side - Content Section */}
          <div className="w-full md:w-3/5">
            <div className="p-6 md:p-8">
              {/* Header */}
              <div className="mb-6">
                <h2 className="text-3xl md:text-4xl font-bold text-primary mb-2">{leader.name}</h2>
                <p className="text-xl text-accent font-semibold">{leader.title}</p>
              </div>

              {/* Bio */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                  <div className="w-1 h-6 bg-accent rounded-full mr-3"></div>
                  About
                </h3>
                <p className="text-gray-700 leading-relaxed">{leader.bio}</p>
              </div>

              {/* Experience & Education */}
              <div className="mb-8">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <div className="w-1 h-6 bg-accent rounded-full mr-3"></div>
                  Experience & Education
                </h3>
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Professional Experience</h4>
                    <p className="text-gray-700 text-sm">{leader.experience}</p>
                  </div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Education</h4>
                    <p className="text-gray-700 text-sm">{leader.education}</p>
                  </div>
                </div>
              </div>

              {/* Certifications */}
              <div className="mb-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <div className="w-1 h-6 bg-accent rounded-full mr-3"></div>
                  Certifications & Specializations
                </h3>
                <div className="grid grid-cols-1 gap-3">
                  {leader.certifications.map((cert, index) => (
                    <div key={index} className="flex items-start bg-white border border-gray-200 rounded-lg p-3">
                      <div className="w-2 h-2 bg-accent rounded-full mr-3 mt-2 flex-shrink-0"></div>
                      <span className="text-gray-700 text-sm">{cert}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamModal;
