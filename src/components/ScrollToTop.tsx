import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * ScrollToTop component that scrolls the page to the top on route change
 * This is a utility component with no visual rendering
 */
export default function ScrollToTop() {
  const { pathname } = useLocation();

  useEffect(() => {
    // Scroll to top when the pathname changes
    window.scrollTo({
      top: 0,
      behavior: 'auto' // Using 'auto' for immediate scrolling
    });
  }, [pathname]);

  // This component doesn't render anything
  return null;
}
