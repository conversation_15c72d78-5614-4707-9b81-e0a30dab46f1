import { useState, useEffect } from 'react';

/**
 * Custom hook to detect if the page has been scrolled beyond a threshold
 * @param threshold - The scroll position threshold (default: 10px)
 * @returns boolean indicating if the page is scrolled beyond the threshold
 */
export const useScrolled = (threshold: number = 10): boolean => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    let ticking = false;

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          const scrolled = window.scrollY > threshold;
          setIsScrolled(scrolled);
          ticking = false;
        });
        ticking = true;
      }
    };

    // Check initial scroll position on mount
    const checkInitialScroll = () => {
      const scrolled = window.scrollY > threshold;
      setIsScrolled(scrolled);
    };

    // Check scroll position immediately and after a short delay to handle page restoration
    checkInitialScroll();
    const timeoutId = setTimeout(checkInitialScroll, 100);

    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, [threshold]);

  return isScrolled;
};
