import { Helmet } from 'react-helmet-async';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, <PERSON>, Clock, Heart, Medal, Users } from 'lucide-react';
import PageWrapper from '../components/PageWrapper';

const Services = () => {
  return (
    <PageWrapper>
      <Helmet>
        <title>Professional Nursing Services Seattle | In-Home Care | Amara Nursing LLC</title>
        <meta name="description" content="Expert in-home nursing care, skilled nursing services, nurse delegation, and adult family home care in Seattle, Lynnwood, Everett, WA. RN-led care for medically complex patients. Licensed, HIPAA-compliant services." />
        <meta name="keywords" content="nursing services Seattle, in-home nursing care, skilled nursing Washington, nurse delegation services, private duty nursing, post-hospitalization care, medically complex care, RN-led care, King County nursing, Snohomish County nursing" />
        <link rel="canonical" href="https://amaracares.com/services" />
        <meta property="og:title" content="Professional Nursing Services Seattle | In-Home Care | Amara Nursing LLC" />
        <meta property="og:description" content="Expert in-home nursing care, skilled nursing services, nurse delegation, and adult family home care in Seattle, Lynnwood, Everett, WA. RN-led care for medically complex patients." />
        <meta property="og:url" content="https://amaracares.com/services" />
        <meta property="og:type" content="website" />
      </Helmet>

      {/* Services Overview */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-primary mb-6 leading-tight">
              Comprehensive Care Solutions
            </h1>
            <p className="text-lg text-text-light">
              Our licensed nursing professionals provide a wide range of services to support health, comfort, and independence. All care is personalized to your unique situation.
            </p>
          </div>

          <div className="space-y-16 max-w-6xl mx-auto">
            {/* Service 1 */}
            <div className="flex flex-col lg:flex-row gap-8 items-center">
              <div className="lg:w-1/2">
                <img
                  src="/images/webp/inhome-service.webp"
                  alt="In-Home Nursing Care"
                  className="w-full h-80 object-cover rounded-lg shadow-md"
                />
              </div>
              <div className="lg:w-1/2">
                <div className="bg-white rounded-lg shadow-lg p-8 border-l-4 border-accent">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-accent/10 p-3 rounded-full flex-shrink-0">
                      <Heart className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-3 text-primary">In-Home Nursing Services</h3>
                      <p className="text-text-light mb-4">
                        Our in-home nursing services are delivered directly in the patient's residence, ideal for individuals who are homebound, recovering from surgery, or managing chronic health conditions. Services include:
                      </p>
                    </div>
                  </div>
                  <ul className="list-disc list-inside text-text-light space-y-2 ml-16">
                    <li>Skilled Nursing Care: Wound care, medication administration, vital sign monitoring, and clinical assessments</li>
                    <li>Private Duty Nursing (PDN): Continuous, personalized care for individuals requiring long-term clinical oversight at home</li>
                    <li>Post-Hospitalization Recovery: Transitional care and monitoring to support safe recovery at home</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Service 2 */}
            <div className="flex flex-col lg:flex-row-reverse gap-8 items-center">
              <div className="lg:w-1/2">
                <img
                  src="/images/webp/onsiteservice.webp"
                  alt="On-Site Nursing"
                  className="w-full h-80 object-cover rounded-lg shadow-md"
                />
              </div>
              <div className="lg:w-1/2">
                <div className="bg-white rounded-lg shadow-lg p-8 border-l-4 border-secondary">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-secondary/10 p-3 rounded-full flex-shrink-0">
                      <Clock className="w-6 h-6 text-secondary" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-3 text-primary">On-Site Nursing</h3>
                      <p className="text-text-light mb-4">
                        Dedicated nursing support for assisted living facilities, rehabilitation centers, and adult family homes on a scheduled or as-needed basis.
                      </p>
                    </div>
                  </div>
                  <ul className="list-disc list-inside text-text-light space-y-2 ml-16">
                    <li>Regular health assessments</li>
                    <li>Care plan development and implementation</li>
                    <li>Staff training and education</li>
                    <li>Medical emergency response</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Service 3 */}
            <div className="flex flex-col lg:flex-row gap-8 items-center">
              <div className="lg:w-1/2">
                <img
                  src="/images/webp/mentalhealth-service.webp"
                  alt="Mental Health and Psychiatric Services"
                  className="w-full h-80 object-cover rounded-lg shadow-md"
                />
              </div>
              <div className="lg:w-1/2">
                <div className="bg-white rounded-lg shadow-lg p-8 border-l-4 border-accent">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-accent/10 p-3 rounded-full flex-shrink-0">
                      <Brain className="w-6 h-6 text-accent" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-3 text-primary">Mental Health & Psychiatric Services</h3>
                      <p className="text-text-light mb-4">
                        Integrated mental health and psychiatric services, including behavioral support and medication management by licensed professionals. Our expert psychiatric care provides mental health assessments, medication management, and support for residents with complex behavioral health needs.
                      </p>
                    </div>
                  </div>
                  <ul className="list-disc list-inside text-text-light space-y-2 ml-16">
                    <li>Comprehensive mental health assessments</li>
                    <li>Psychiatric medication management</li>
                    <li>Behavioral support and intervention</li>
                    <li>Complex behavioral health needs support</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Service 4 */}
            <div className="flex flex-col lg:flex-row-reverse gap-8 items-center">
              <div className="lg:w-1/2">
                <img
                  src="/images/webp/specialized-service.webp"
                  alt="Specialized Care"
                  className="w-full h-80 object-cover rounded-lg shadow-md"
                />
              </div>
              <div className="lg:w-1/2">
                <div className="bg-white rounded-lg shadow-lg p-8 border-l-4 border-tertiary">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-tertiary/20 p-3 rounded-full flex-shrink-0">
                      <Medal className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-3 text-primary">Specialized Care</h3>
                      <p className="text-text-light mb-4">
                        Tailored nursing services for individuals with complex or specialized healthcare needs, including chronic disease management.
                      </p>
                    </div>
                  </div>
                  <ul className="list-disc list-inside text-text-light space-y-2 ml-16">
                    <li>Diabetes management and education</li>
                    <li>Cardiac care and monitoring</li>
                    <li>Respiratory therapy support</li>
                    <li>Palliative and end-of-life care</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Service 5 */}
            <div className="flex flex-col lg:flex-row gap-8 items-center">
              <div className="lg:w-1/2">
                <img
                  src="/images/webp/delegation-service.webp"
                  alt="Nurse Delegation"
                  className="w-full h-80 object-cover rounded-lg shadow-md"
                />
              </div>
              <div className="lg:w-1/2">
                <div className="bg-white rounded-lg shadow-lg p-8 border-l-4 border-primary">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="bg-primary/10 p-3 rounded-full flex-shrink-0">
                      <Users className="w-6 h-6 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold mb-3 text-primary">Nurse Delegation</h3>
                      <p className="text-text-light mb-4">
                        Our RNs provide delegation services to support caregivers in assisted living facilities, adult family homes, and in-home care settings.
                      </p>
                    </div>
                  </div>
                  <ul className="list-disc list-inside text-text-light space-y-2 ml-16">
                    <li>Caregiver training and certification</li>
                    <li>Medication administration delegation</li>
                    <li>Regular assessments and monitoring</li>
                    <li>Documentation and compliance support</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>


      {/* Service Area */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Service Areas</h2>
            <p className="text-text-light">
              We proudly serve King, Snohomish, and Pierce counties, offering flexible scheduling to accommodate your needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="bg-neutral rounded-lg p-6 text-center">
              <h3 className="font-bold text-lg mb-3">King County</h3>
              <p className="text-text-light text-sm">Seattle, Bellevue, Redmond, Kirkland, Renton, Kent, Federal Way, Bothell, Shoreline, Burien</p>
            </div>
            <div className="bg-neutral rounded-lg p-6 text-center">
              <h3 className="font-bold text-lg mb-3">Snohomish County</h3>
              <p className="text-text-light text-sm">Everett, Lynnwood, Edmonds, Mukilteo, Mill Creek, Bothell, Woodinville, Mountlake Terrace</p>
            </div>
            <div className="bg-neutral rounded-lg p-6 text-center">
              <h3 className="font-bold text-lg mb-3">Pierce County</h3>
              <p className="text-text-light text-sm">Tacoma, Lakewood, Puyallup, Auburn, Federal Way, University Place, Sumner, Bonney Lake</p>
            </div>
          </div>

          <div className="text-center mt-8">
            <p className="text-text-light mb-6">
              Not sure if we service your area? Contact us to discuss your location and needs.
            </p>
            <Link to="/contact" className="inline-flex items-center text-primary font-semibold hover:text-accent transition-colors">
              Check Service Availability <ArrowRight size={16} className="ml-2" />
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-tertiary/10 py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-primary mb-4">Ready to Discuss Your Care Needs?</h2>
          <p className="text-lg text-text-light mb-8 max-w-2xl mx-auto">
            Our professional nursing team is ready to provide the compassionate, expert care you deserve. Contact us today to schedule a consultation.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Link to="/contact" className="btn-primary">
              Contact Us
            </Link>
            <Link to="/afh" className="btn bg-white text-primary border-2 border-primary hover:bg-primary hover:text-white transition-colors">
              Learn About Our AFH
            </Link>
          </div>
        </div>
      </section>
    </PageWrapper>
  );
};

export default Services;
