import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { ArrowLeft, ArrowRight, Calendar, Clock } from 'lucide-react';
import { blogPosts } from '../data/blogData';
import BlogCard from '../components/BlogCard';

const BlogPost = () => {
  const { slug } = useParams<{ slug: string }>();
  
  // Find the current post based on slug
  const currentPost = blogPosts.find(post => post.slug === slug);
  
  // Handle 404 if post not found
  if (!currentPost) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-3xl font-bold text-primary mb-6">Blog Post Not Found</h1>
        <p className="text-text-light mb-8">The blog post you're looking for doesn't exist or has been moved.</p>
        <Link to="/blog" className="btn-primary">
          Back to Blog
        </Link>
      </div>
    );
  }
  
  // Find previous and next posts for navigation
  const currentIndex = blogPosts.findIndex(post => post.slug === slug);
  const prevPost = currentIndex > 0 ? blogPosts[currentIndex - 1] : null;
  const nextPost = currentIndex < blogPosts.length - 1 ? blogPosts[currentIndex + 1] : null;
  
  return (
    <>
      <Helmet>
        <title>{currentPost.title} | Amara Nursing LLC Blog</title>
        <meta name="description" content={currentPost.excerpt} />
      </Helmet>
      
      {/* Back to Blog Link */}
      <div className="bg-neutral pt-8">
        <div className="container mx-auto px-4">
          <Link to="/blog" className="inline-flex items-center text-primary hover:text-accent transition-colors">
            <ArrowLeft size={16} className="mr-2" /> Back to Blog
          </Link>
        </div>
      </div>
      
      {/* Hero Section */}
      <section className="bg-neutral relative overflow-hidden">
        <div className="container mx-auto px-4 py-8 md:py-16">
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center text-text-light text-sm mb-4">
              <Calendar size={16} className="mr-1" />
              <span>{currentPost.date}</span>
              <span className="mx-2">•</span>
              <Clock size={16} className="mr-1" />
              <span>5 min read</span>
            </div>
            
            <h1 className="text-3xl md:text-4xl font-bold text-primary mb-6 leading-tight">
              {currentPost.title}
            </h1>
            
            <p className="text-lg text-text-light mb-8">
              {currentPost.excerpt}
            </p>
          </div>
        </div>
      </section>
      
      {/* Feature Image */}
      <div className="bg-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="bg-primary/10 rounded-lg h-64 md:h-96 flex items-center justify-center text-primary/30 p-4 text-center">
              {currentPost.thumbnailDesc}
            </div>
          </div>
        </div>
      </div>
      
      {/* Blog Content */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="prose prose-lg max-w-none">
              {currentPost.content.map((paragraph, index) => {
                // Check if paragraph is a heading (starts with ##)
                if (paragraph.startsWith('##')) {
                  return (
                    <h2 key={index} className="text-2xl font-bold text-primary mt-8 mb-4">
                      {paragraph.replace('##', '').trim()}
                    </h2>
                  );
                }
                return <p key={index} className="mb-4 text-text-light">{paragraph}</p>;
              })}
            </div>
          </div>
        </div>
      </section>
      
      {/* Post Navigation */}
      <section className="bg-neutral py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="flex flex-col sm:flex-row justify-between items-center">
              {prevPost ? (
                <Link 
                  to={`/blog/${prevPost.slug}`} 
                  className="flex items-center text-primary hover:text-accent transition-colors mb-4 sm:mb-0"
                >
                  <ArrowLeft size={16} className="mr-2" /> Previous Post
                </Link>
              ) : <div></div>}
              
              {nextPost && (
                <Link 
                  to={`/blog/${nextPost.slug}`} 
                  className="flex items-center text-primary hover:text-accent transition-colors"
                >
                  Next Post <ArrowRight size={16} className="ml-2" />
                </Link>
              )}
            </div>
          </div>
        </div>
      </section>
      
      {/* Related Posts (simplified) */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-primary mb-8 text-center">More Articles You Might Enjoy</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {blogPosts
                .filter(post => post.id !== currentPost.id)
                .slice(0, 2)
                .map(post => (
                  <BlogCard
                    key={post.id}
                    id={post.id}
                    title={post.title}
                    slug={post.slug}
                    date={post.date}
                    excerpt={post.excerpt}
                    thumbnailDesc={post.thumbnailDesc}
                  />
                ))}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default BlogPost;
