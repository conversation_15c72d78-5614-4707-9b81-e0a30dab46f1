import { Helmet } from 'react-helmet-async';
import { blogPosts } from '../data/blogData';
import BlogCard from '../components/BlogCard';

const Blog = () => {
  return (
    <>
      <Helmet>
        <title>Blog | Amara Nursing LLC</title>
        <meta name="description" content="Read the latest articles on healthcare, nursing best practices, and tips for patient care from the experts at Amara Nursing LLC." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-neutral relative overflow-hidden">
        <div className="container mx-auto px-4 py-16 md:py-24">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-primary mb-6 leading-tight">
              Our Blog
            </h1>
            <p className="text-lg text-text-light mb-8">
              Insights, resources, and expertise from our team of healthcare professionals.
            </p>
          </div>
        </div>
        {/* Abstract background element */}
        <div className="absolute top-0 right-0 -z-10 w-1/3 h-full bg-tertiary/30 rounded-bl-[100px] opacity-50"></div>
      </section>

      {/* Blog Posts Section */}
      <section className="bg-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => (
              <BlogCard
                key={post.id}
                id={post.id}
                title={post.title}
                slug={post.slug}
                date={post.date}
                excerpt={post.excerpt}
                thumbnailDesc={post.thumbnailDesc}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="bg-tertiary/10 py-12">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-2xl font-bold text-primary mb-4">Have Questions About Our Services?</h2>
          <p className="text-text-light mb-6 max-w-2xl mx-auto">
            Our team of nursing professionals is ready to assist you with any questions about our in-home nursing care, adult family home, or other services.
          </p>
          <a href="/contact" className="btn-primary inline-block">
            Contact Us Today
          </a>
        </div>
      </section>
    </>
  );
};

export default Blog;
