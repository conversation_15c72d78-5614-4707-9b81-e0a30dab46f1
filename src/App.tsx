import { useEffect } from 'react';
import { Routes, Route, useLocation } from 'react-router-dom';
import { AnimatePresence } from 'framer-motion';
import Layout from './components/Layout';
import Home from './pages/Home';
import Services from './pages/Services';
import AdultFamilyHome from './pages/AdultFamilyHome';
import About from './pages/About';
import Contact from './pages/Contact';

import PrivacyPolicy from './pages/PrivacyPolicy';
import TermsOfService from './pages/TermsOfService';
import NotFound from './pages/NotFound';

function App() {
  const location = useLocation();

  // Load Google Fonts
  useEffect(() => {
    // Nunito Sans for headings and navigation
    const nunitoSans = document.createElement('link');
    nunitoSans.href = 'https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;600;700&display=swap';
    nunitoSans.rel = 'stylesheet';
    document.head.appendChild(nunitoSans);

    // Open Sans for body text
    const openSans = document.createElement('link');
    openSans.href = 'https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600&display=swap';
    openSans.rel = 'stylesheet';
    document.head.appendChild(openSans);

    return () => {
      document.head.removeChild(nunitoSans);
      document.head.removeChild(openSans);
    };
  }, []);

  return (
    <AnimatePresence mode="wait" initial={false}>
      <Routes location={location} key={location.pathname}>
        <Route path="/" element={<Layout />}>
          <Route index element={<Home />} />
          <Route path="services" element={<Services />} />
          <Route path="afh" element={<AdultFamilyHome />} />
          <Route path="about" element={<About />} />
          <Route path="contact" element={<Contact />} />
          <Route path="privacy-policy" element={<PrivacyPolicy />} />
          <Route path="terms-of-service" element={<TermsOfService />} />
          <Route path="*" element={<NotFound />} />
        </Route>
      </Routes>
    </AnimatePresence>
  );
}

export default App;
