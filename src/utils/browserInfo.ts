// Browser information collection utilities
export interface BrowserInfo {
  userAgent: string;
  language: string;
  platform: string;
  cookieEnabled: boolean;
  onLine: boolean;
  screen: {
    width: number;
    height: number;
    colorDepth: number;
    pixelRatio: number;
  };
  timezone: string;
  timestamp: string;
  referrer: string;
  browserName: string;
  browserVersion: string;
  osName: string;
  deviceType: string;
}

export interface LocationData {
  ip?: string;
  city?: string;
  region?: string;
  country?: string;
  country_code?: string;
  postal?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  isp?: string;
  org?: string;
  as?: string;
  error?: string;
}

// Parse user agent to extract browser and OS information
function parseUserAgent(userAgent: string) {
  const browserRegexes = [
    { name: 'Chrome', regex: /Chrome\/([0-9.]+)/ },
    { name: 'Firefox', regex: /Firefox\/([0-9.]+)/ },
    { name: 'Safari', regex: /Version\/([0-9.]+).*Safari/ },
    { name: 'Edge', regex: /Edg\/([0-9.]+)/ },
    { name: 'Opera', regex: /OPR\/([0-9.]+)/ },
    { name: 'Internet Explorer', regex: /MSIE ([0-9.]+)/ },
  ];

  const osRegexes = [
    { name: 'Windows', regex: /Windows NT ([0-9.]+)/ },
    { name: 'macOS', regex: /Mac OS X ([0-9_.]+)/ },
    { name: 'Linux', regex: /Linux/ },
    { name: 'Android', regex: /Android ([0-9.]+)/ },
    { name: 'iOS', regex: /OS ([0-9_]+)/ },
  ];

  let browserName = 'Unknown';
  let browserVersion = 'Unknown';
  let osName = 'Unknown';

  // Detect browser
  for (const browser of browserRegexes) {
    const match = userAgent.match(browser.regex);
    if (match) {
      browserName = browser.name;
      browserVersion = match[1];
      break;
    }
  }

  // Detect OS
  for (const os of osRegexes) {
    const match = userAgent.match(os.regex);
    if (match) {
      osName = os.name;
      break;
    }
  }

  return { browserName, browserVersion, osName };
}

// Detect device type
function getDeviceType(): string {
  const userAgent = navigator.userAgent;
  
  if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
    return 'Tablet';
  }
  
  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
    return 'Mobile';
  }
  
  return 'Desktop';
}

// Collect comprehensive browser information
export const getBrowserInfo = (): BrowserInfo => {
  const nav = navigator as any;
  const { browserName, browserVersion, osName } = parseUserAgent(nav.userAgent);
  
  return {
    userAgent: nav.userAgent,
    language: nav.language || nav.userLanguage || 'Unknown',
    platform: nav.platform || 'Unknown',
    cookieEnabled: nav.cookieEnabled,
    onLine: nav.onLine,
    screen: {
      width: screen.width,
      height: screen.height,
      colorDepth: screen.colorDepth,
      pixelRatio: window.devicePixelRatio || 1,
    },
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    timestamp: new Date().toISOString(),
    referrer: document.referrer || 'Direct',
    browserName,
    browserVersion,
    osName,
    deviceType: getDeviceType(),
  };
};

// Get location data from IP geolocation service
export const getLocationData = async (): Promise<LocationData> => {
  try {
    // Using ipapi.co for free IP geolocation
    const response = await fetch('https://ipapi.co/json/', {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (response.ok) {
      const data = await response.json();
      
      // Handle rate limiting or errors from the API
      if (data.error) {
        console.warn('IP geolocation error:', data.reason);
        return { error: data.reason };
      }

      return {
        ip: data.ip,
        city: data.city,
        region: data.region,
        country: data.country_name,
        country_code: data.country_code,
        postal: data.postal,
        latitude: data.latitude,
        longitude: data.longitude,
        timezone: data.timezone,
        isp: data.org,
        org: data.org,
        as: data.asn,
      };
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.warn('Could not get location data:', error);
    return { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

// Format browser info for display
export const formatBrowserInfo = (browserInfo: BrowserInfo): string => {
  return `${browserInfo.browserName} ${browserInfo.browserVersion} on ${browserInfo.osName} (${browserInfo.deviceType})`;
};

// Format location info for display
export const formatLocationInfo = (locationData: LocationData): string => {
  if (locationData.error) {
    return `Location: Unable to determine (${locationData.error})`;
  }

  const parts = [];
  if (locationData.city) parts.push(locationData.city);
  if (locationData.region) parts.push(locationData.region);
  if (locationData.country) parts.push(locationData.country);
  
  return parts.length > 0 ? parts.join(', ') : 'Location: Unknown';
};
