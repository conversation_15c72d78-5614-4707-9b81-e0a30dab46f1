// ES Module version of resolutions.js
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get __dirname equivalent in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read package.json
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// Set React and React DOM to 18.2.0
packageJson.dependencies.react = '18.2.0';
packageJson.dependencies['react-dom'] = '18.2.0';

// Update devDependencies to match
if (packageJson.devDependencies && packageJson.devDependencies['@types/react']) {
  packageJson.devDependencies['@types/react'] = '^18.2.0';
}
if (packageJson.devDependencies && packageJson.devDependencies['@types/react-dom']) {
  packageJson.devDependencies['@types/react-dom'] = '^18.2.0';
}

// Write the updated package.json
fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

console.log('Updated React and React DOM to v18.2.0 for compatibility'); 