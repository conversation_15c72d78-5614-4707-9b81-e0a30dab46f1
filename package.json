{"name": "amara-nursing", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node resolutions.js && tsc --noEmitOnError false && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@hookform/resolvers": "^5.0.1", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "lucide-react": "^0.510.0", "react": "18.2.0", "react-dom": "18.2.0", "react-error-boundary": "^6.0.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.56.4", "react-router-dom": "^7.6.0", "resend": "^4.5.2", "zod": "^3.25.17"}, "devDependencies": {"@getmocha/vite-plugins": "latest", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "vite": "^6.2.1"}, "resolutions": {"react": "18.2.0", "react-dom": "18.2.0"}}