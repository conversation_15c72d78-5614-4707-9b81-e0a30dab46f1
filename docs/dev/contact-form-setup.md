# Contact Form Implementation Guide

## 1. Supabase Database Schema

```sql
-- Create contact_submissions table
CREATE TABLE contact_submissions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Form data
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(50),
  subject VARCHAR(500) NOT NULL,
  message TEXT NOT NULL,
  
  -- Technical data
  ip_address INET,
  user_agent TEXT,
  browser_info JSONB,
  location_data JSONB,
  
  -- Status tracking
  status VARCHAR(50) DEFAULT 'new',
  processed_at TIMESTAMP WITH TIME ZONE,
  
  -- Security
  spam_score DECIMAL(3,2) DEFAULT 0.0,
  verified BOOLEAN DEFAULT false
);

-- Create index for performance
CREATE INDEX idx_contact_submissions_created_at ON contact_submissions(created_at DESC);
CREATE INDEX idx_contact_submissions_status ON contact_submissions(status);

-- Row Level Security (RLS)
ALTER TABLE contact_submissions ENABLE ROW LEVEL SECURITY;

-- Policy: Only service role can insert/read
CREATE POLICY "Service role access" ON contact_submissions
  FOR ALL USING (auth.role() = 'service_role');
```

## 2. Environment Variables

```env
# Supabase
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Resend
RESEND_API_KEY=your_resend_api_key

# App Config
VITE_CONTACT_EMAIL=<EMAIL>
VITE_SITE_URL=https://yourdomain.com
```

## 3. Supabase Edge Function

```typescript
// supabase/functions/contact-form/index.ts
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface ContactFormData {
  name: string
  email: string
  phone?: string
  subject: string
  message: string
  browserInfo: any
  ipAddress: string
  locationData?: any
}

serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { name, email, phone, subject, message, browserInfo, locationData } = await req.json()

    // Get IP address
    const ipAddress = req.headers.get('x-forwarded-for') || 
                     req.headers.get('x-real-ip') || 
                     'unknown'

    // Basic spam detection
    const spamScore = calculateSpamScore({ name, email, message })
    
    // Insert into database
    const { data, error } = await supabase
      .from('contact_submissions')
      .insert({
        name,
        email,
        phone,
        subject,
        message,
        ip_address: ipAddress,
        user_agent: req.headers.get('user-agent'),
        browser_info: browserInfo,
        location_data: locationData,
        spam_score: spamScore,
        verified: spamScore < 0.5
      })
      .select()

    if (error) throw error

    // Send emails if not spam
    if (spamScore < 0.5) {
      await sendNotificationEmails({ name, email, phone, subject, message })
    }

    return new Response(
      JSON.stringify({ success: true, id: data[0].id }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200 
      }
    )

  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400 
      }
    )
  }
})

function calculateSpamScore(data: any): number {
  let score = 0
  
  // Check for spam indicators
  if (data.message.includes('http://') || data.message.includes('https://')) score += 0.3
  if (data.name.length < 2) score += 0.2
  if (!data.email.includes('@')) score += 0.5
  if (data.message.length < 10) score += 0.2
  
  return Math.min(score, 1.0)
}

async function sendNotificationEmails(formData: any) {
  const resendApiKey = Deno.env.get('RESEND_API_KEY')
  
  // Send notification to business
  await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: '<EMAIL>',
      to: ['<EMAIL>'],
      subject: `New Contact Form: ${formData.subject}`,
      html: `
        <h2>New Contact Form Submission</h2>
        <p><strong>Name:</strong> ${formData.name}</p>
        <p><strong>Email:</strong> ${formData.email}</p>
        <p><strong>Phone:</strong> ${formData.phone || 'Not provided'}</p>
        <p><strong>Subject:</strong> ${formData.subject}</p>
        <p><strong>Message:</strong></p>
        <p>${formData.message.replace(/\n/g, '<br>')}</p>
      `
    })
  })

  // Send confirmation to user
  await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${resendApiKey}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      from: '<EMAIL>',
      to: [formData.email],
      subject: 'Thank you for contacting Amara Nursing LLC',
      html: `
        <h2>Thank you for your message</h2>
        <p>Dear ${formData.name},</p>
        <p>We have received your message and will respond within 24 hours.</p>
        <p>Best regards,<br>Amara Nursing LLC Team</p>
      `
    })
  })
}
```
