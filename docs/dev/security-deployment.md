# Security & Deployment Guide

## 🔒 Security Measures

### 1. Rate Limiting
```typescript
// In Supabase Edge Function
const rateLimitKey = `contact_form_${ipAddress}`
const rateLimitWindow = 3600 // 1 hour
const maxRequests = 5

// Check rate limit using Supabase storage or external service
const { data: rateLimitData } = await supabase
  .from('rate_limits')
  .select('count, window_start')
  .eq('key', rateLimitKey)
  .single()

if (rateLimitData && rateLimitData.count >= maxRequests) {
  return new Response(
    JSON.stringify({ error: 'Too many requests. Please try again later.' }),
    { status: 429, headers: corsHeaders }
  )
}
```

### 2. Input Sanitization
```typescript
import DOMPurify from 'isomorphic-dompurify'

function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, { ALLOWED_TAGS: [] })
}

// Apply to all form inputs
const sanitizedData = {
  name: sanitizeInput(formData.name),
  email: sanitizeInput(formData.email),
  subject: sanitizeInput(formData.subject),
  message: sanitizeInput(formData.message)
}
```

### 3. CSRF Protection
```typescript
// Generate CSRF token on form load
const generateCSRFToken = (): string => {
  return crypto.randomUUID()
}

// Include in form submission
const csrfToken = generateCSRFToken()
```

### 4. Honeypot Field
```typescript
// Add hidden field to catch bots
<input
  type="text"
  name="website"
  style={{ display: 'none' }}
  tabIndex={-1}
  autoComplete="off"
/>

// Check in backend
if (formData.website) {
  // Likely spam, reject silently
  return new Response(JSON.stringify({ success: true }), { status: 200 })
}
```

## 🚀 Deployment Steps

### 1. Supabase Setup
```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Initialize project
supabase init

# Link to your project
supabase link --project-ref your-project-ref

# Deploy database schema
supabase db push

# Deploy edge function
supabase functions deploy contact-form
```

### 2. Environment Variables
```bash
# Production .env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
RESEND_API_KEY=re_your_resend_key
VITE_CONTACT_EMAIL=<EMAIL>
VITE_SITE_URL=https://amaracares.com
```

### 3. Domain Configuration
```typescript
// Update CORS in edge function for production
const corsHeaders = {
  'Access-Control-Allow-Origin': 'https://amaracares.com',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
}
```

## 📊 Monitoring & Analytics

### 1. Error Tracking
```typescript
// Add to edge function
try {
  // ... form processing
} catch (error) {
  // Log error for monitoring
  console.error('Contact form error:', {
    error: error.message,
    stack: error.stack,
    formData: { ...formData, message: '[REDACTED]' },
    timestamp: new Date().toISOString()
  })
  
  // Send to monitoring service (optional)
  await fetch('https://api.sentry.io/...', {
    method: 'POST',
    body: JSON.stringify({ error: error.message })
  })
}
```

### 2. Success Metrics
```sql
-- Query for form submission analytics
SELECT 
  DATE(created_at) as date,
  COUNT(*) as total_submissions,
  COUNT(*) FILTER (WHERE verified = true) as verified_submissions,
  AVG(spam_score) as avg_spam_score
FROM contact_submissions 
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;
```

## 🏥 HIPAA Considerations

### Important Notes:
1. **Contact forms are NOT PHI** - General inquiries don't contain protected health information
2. **If collecting medical info** - You'd need a BAA (Business Associate Agreement) with providers
3. **Current setup is appropriate** for general business contact forms

### Additional Security for Healthcare:
```typescript
// Add encryption for sensitive fields (if needed)
import CryptoJS from 'crypto-js'

const encryptSensitiveData = (data: string): string => {
  return CryptoJS.AES.encrypt(data, process.env.ENCRYPTION_KEY).toString()
}

// Use for any potentially sensitive information
const encryptedMessage = encryptSensitiveData(formData.message)
```

## 🧪 Testing Checklist

### Frontend Testing
- [ ] Form validation works correctly
- [ ] Error messages display properly
- [ ] Success state shows confirmation
- [ ] Loading states work
- [ ] Mobile responsiveness
- [ ] Accessibility (screen readers)

### Backend Testing
- [ ] Form submissions save to database
- [ ] Emails send successfully
- [ ] Rate limiting works
- [ ] Spam detection functions
- [ ] Error handling graceful

### Security Testing
- [ ] SQL injection attempts blocked
- [ ] XSS attempts sanitized
- [ ] Rate limiting enforced
- [ ] CORS properly configured
- [ ] Honeypot catches bots

## 📈 Performance Optimization

### 1. Frontend Optimization
```typescript
// Lazy load location data
const getLocationDataLazy = useMemo(() => 
  debounce(getLocationData, 1000), []
)

// Preload on form focus
const handleFormFocus = useCallback(() => {
  getLocationDataLazy()
}, [getLocationDataLazy])
```

### 2. Database Optimization
```sql
-- Add indexes for common queries
CREATE INDEX CONCURRENTLY idx_contact_submissions_email 
ON contact_submissions(email);

CREATE INDEX CONCURRENTLY idx_contact_submissions_spam_score 
ON contact_submissions(spam_score) WHERE spam_score > 0.5;
```

## 💡 Alternative Solutions Comparison

| Solution | Cost | Complexity | Features | HIPAA Ready |
|----------|------|------------|----------|-------------|
| **Supabase + Resend** | Free/$25 | Medium | Full-featured | With BAA |
| **Google Sheets + EmailJS** | Free | Low | Basic | No |
| **Netlify Forms + SendGrid** | $19+ | Low | Medium | No |
| **AWS SES + Lambda** | ~$5 | High | Full-featured | Yes |
| **Formspree** | $8+ | Very Low | Basic | No |

## 🎯 Recommended Next Steps

1. **Phase 1**: Implement basic Supabase + Resend solution
2. **Phase 2**: Add advanced spam detection and analytics
3. **Phase 3**: Consider HIPAA compliance if handling PHI
4. **Phase 4**: Add automated follow-up workflows

This solution provides enterprise-grade reliability while maintaining simplicity and cost-effectiveness for your healthcare business needs.
