# Page Transitions Troubleshooting Guide

## Common Issues and Solutions

### 🔥 Issue: Double-Flash Effect
**Symptoms:** Content appears, then animates, then re-renders
**Root Cause:** AnimatePresence is not controlling component mounting/unmounting properly

**Solution:**
```typescript
// ❌ Wrong: AnimatePresence inside component
const Layout = () => (
  <div>
    <Header />
    <AnimatePresence>
      <Outlet />
    </AnimatePresence>
  </div>
);

// ✅ Correct: AnimatePresence wrapping Routes
const App = () => (
  <AnimatePresence mode="wait" initial={false}>
    <Routes location={location} key={location.pathname}>
      <Route path="/" element={<Layout />} />
    </Routes>
  </AnimatePresence>
);
```

### 🔥 Issue: Animations Not Triggering
**Symptoms:** Pages change instantly without animation
**Root Cause:** Missing key prop or incorrect location usage

**Solution:**
```typescript
// ❌ Wrong: No key or location
<Routes>
  <Route path="/" element={<Layout />} />
</Routes>

// ✅ Correct: With location and key
const location = useLocation();
<Routes location={location} key={location.pathname}>
  <Route path="/" element={<Layout />} />
</Routes>
```

### 🔥 Issue: Overlapping Animations
**Symptoms:** New page appears before old page exits
**Root Cause:** Missing `mode="wait"` on AnimatePresence

**Solution:**
```typescript
// ❌ Wrong: No mode specified
<AnimatePresence>
  <Routes>...</Routes>
</AnimatePresence>

// ✅ Correct: With wait mode
<AnimatePresence mode="wait" initial={false}>
  <Routes>...</Routes>
</AnimatePresence>
```

### 🔥 Issue: Layout Shift During Animation
**Symptoms:** Page jumps or shifts during transition
**Root Cause:** Animating layout-affecting properties

**Solution:**
```typescript
// ❌ Wrong: Animating layout properties
const badVariants = {
  initial: { height: 0, width: 0 },
  in: { height: 'auto', width: 'auto' }
};

// ✅ Correct: Transform-only animations
const goodVariants = {
  initial: { opacity: 0, y: 20, scale: 0.98 },
  in: { opacity: 1, y: 0, scale: 1 }
};
```

### 🔥 Issue: Performance Problems
**Symptoms:** Janky animations, low FPS
**Root Cause:** Complex animations or too many animated elements

**Solutions:**
1. **Simplify animations:**
```typescript
// ❌ Complex: Multiple properties
const complex = {
  initial: { opacity: 0, x: 100, y: 50, rotate: 45, scale: 0.5 },
  in: { opacity: 1, x: 0, y: 0, rotate: 0, scale: 1 }
};

// ✅ Simple: Essential properties only
const simple = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 }
};
```

2. **Add CSS optimizations:**
```css
.page-transition {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}
```

### 🔥 Issue: First Page Load Animation
**Symptoms:** Unwanted animation when site first loads
**Root Cause:** Missing `initial={false}` on AnimatePresence

**Solution:**
```typescript
// ❌ Wrong: Animates on first load
<AnimatePresence mode="wait">

// ✅ Correct: No first-load animation
<AnimatePresence mode="wait" initial={false}>
```

### 🔥 Issue: Static Elements Animating
**Symptoms:** Header/Footer animate with page content
**Root Cause:** Static elements inside animated wrapper

**Solution:**
```typescript
// ❌ Wrong: Everything inside PageWrapper
<PageWrapper>
  <Header />
  <main>Content</main>
  <Footer />
</PageWrapper>

// ✅ Correct: Only content animated
<div>
  <Header />
  <PageWrapper>
    <main>Content</main>
  </PageWrapper>
  <Footer />
</div>
```

## Debugging Steps

### Step 1: Check AnimatePresence Setup
```typescript
// Verify these are correct:
const location = useLocation(); // ✓ Hook called
<AnimatePresence mode="wait" initial={false}> // ✓ Props set
  <Routes location={location} key={location.pathname}> // ✓ Location passed
```

### Step 2: Verify Component Structure
```
App.tsx
├── AnimatePresence (mode="wait" initial={false})
│   └── Routes (location={location} key={location.pathname})
│       └── Route (path="/" element={<Layout />})
│           └── Layout
│               └── Outlet
│                   └── PageWrapper (motion.div)
│                       └── Page Content
```

### Step 3: Test Animation Variants
```typescript
// Add console.logs to debug
const pageVariants = {
  initial: (custom) => {
    console.log('Initial animation', custom);
    return { opacity: 0, y: 20 };
  },
  in: (custom) => {
    console.log('In animation', custom);
    return { opacity: 1, y: 0 };
  },
  out: (custom) => {
    console.log('Out animation', custom);
    return { opacity: 0, y: -20 };
  }
};
```

### Step 4: Check Browser DevTools
1. **Performance tab:** Look for layout thrashing
2. **Elements tab:** Verify DOM structure
3. **Console:** Check for React warnings
4. **Network tab:** Ensure no blocking requests

## Performance Optimization

### CSS Optimizations
```css
/* Add to animated elements */
.page-wrapper {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  .page-wrapper {
    animation: none !important;
    transition: none !important;
  }
}
```

### React Optimizations
```typescript
// Memoize expensive components
const PageWrapper = memo(({ children }) => (
  <motion.div variants={pageVariants}>
    {children}
  </motion.div>
));

// Use layout effect for immediate updates
useLayoutEffect(() => {
  // Immediate DOM updates
}, [location.pathname]);
```

## Testing Checklist

### Manual Testing
- [ ] Navigate between all routes
- [ ] Test on mobile devices
- [ ] Check with slow network (3G)
- [ ] Verify with reduced motion settings
- [ ] Test browser back/forward buttons

### Automated Testing
```typescript
// Test route transitions
test('page transitions work correctly', async () => {
  render(<App />);
  
  // Navigate to different route
  fireEvent.click(screen.getByText('About'));
  
  // Check animation classes are applied
  await waitFor(() => {
    expect(screen.getByTestId('page-wrapper')).toHaveClass('motion-div');
  });
});
```

## Browser Compatibility

| Browser | Support | Notes |
|---------|---------|-------|
| Chrome 60+ | ✅ Full | Best performance |
| Firefox 55+ | ✅ Full | Good performance |
| Safari 12+ | ✅ Full | May need prefixes |
| Edge 79+ | ✅ Full | Chromium-based |
| IE 11 | ❌ None | Not supported |

## When to Seek Help

1. **Animations work but feel janky** → Performance optimization needed
2. **Double-flash persists** → Architecture issue, review setup
3. **Specific routes don't animate** → Check PageWrapper implementation
4. **Mobile performance poor** → Simplify animations
5. **Accessibility concerns** → Add reduced motion support

## Emergency Rollback

If transitions cause critical issues:

1. **Quick disable:**
```typescript
// Temporarily disable animations
<AnimatePresence mode="wait" initial={false}>
  <div style={{ opacity: 1 }}> {/* Static wrapper */}
    <Routes location={location} key={location.pathname}>
      {/* Routes */}
    </Routes>
  </div>
</AnimatePresence>
```

2. **Remove PageWrapper:**
```typescript
// Remove from all pages temporarily
const Home = () => (
  <div> {/* Replace PageWrapper with div */}
    {/* Content */}
  </div>
);
```

---

*For additional help, check the main guide or contact the development team*
