# Page Transitions with React Router & Framer Motion

## Overview

This guide explains how we implemented smooth page transitions using React Router and Framer Motion, eliminating the double-flash issue and creating seamless navigation experiences.

## The Problem We Solved

### Before: Double-Flash Issue
Our original implementation had several problems:
- **Loading screen approach**: 800ms artificial delay on every route change
- **Double-flash effect**: Content would flash, then animate, then re-render
- **Poor UX**: Users saw loading spinners instead of smooth transitions
- **Manual CSS hacks**: Custom keyframe animations that were hard to maintain

### After: Smooth Framer Motion Transitions
- **Instant transitions**: No artificial delays
- **No content flash**: Proper animation sequencing
- **Smooth fade + slide**: Professional-looking transitions
- **Library-based**: Using Framer Motion's robust animation system

## Architecture Overview

```
App.tsx (AnimatePresence wrapper)
├── Routes (with location key)
│   └── Layout (static elements)
│       └── Outlet (animated content)
│           └── PageWrapper (motion.div)
│               └── Page Content
```

## Key Implementation Details

### 1. App.tsx - The Foundation

```typescript
import { AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';

function App() {
  const location = useLocation();

  return (
    <AnimatePresence mode="wait" initial={false}>
      <Routes location={location} key={location.pathname}>
        {/* Routes */}
      </Routes>
    </AnimatePresence>
  );
}
```

**Critical Points:**
- `AnimatePresence` wraps `Routes`, not individual components
- `mode="wait"` ensures exit completes before enter starts
- `location.pathname` as key triggers animations on route changes
- `initial={false}` prevents animation on first load

### 2. PageWrapper Component

```typescript
const pageVariants = {
  initial: { opacity: 0, y: 20, scale: 0.98 },
  in: { opacity: 1, y: 0, scale: 1 },
  out: { opacity: 0, y: -20, scale: 1.02 },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};
```

**Animation Details:**
- **Fade**: Opacity 0 → 1 → 0
- **Slide**: Y-axis movement (20px → 0 → -20px)
- **Scale**: Subtle zoom effect (0.98 → 1 → 1.02)
- **Timing**: 400ms with 'anticipate' easing

### 3. Layout Structure

```typescript
// Layout.tsx - Static elements outside animation
const Layout = () => (
  <div className="flex flex-col min-h-screen">
    <Header />  {/* Static */}
    <main>
      <Outlet />  {/* Animated content */}
    </main>
    <Footer />  {/* Static */}
  </div>
);
```

## Best Practices

### ✅ Do's

1. **Wrap Routes, not components**: AnimatePresence should wrap the entire routing system
2. **Use location.pathname as key**: This triggers animations on route changes
3. **Keep static elements outside**: Header/Footer shouldn't animate
4. **Use mode="wait"**: Prevents overlapping animations
5. **Optimize transition duration**: 300-500ms is ideal for web
6. **Test on slower devices**: Ensure smooth performance

### ❌ Don'ts

1. **Don't nest AnimatePresence**: Causes timing conflicts
2. **Don't animate layout elements**: Keep header/footer static
3. **Don't use long durations**: >600ms feels sluggish
4. **Don't forget initial={false}**: Prevents unwanted first-load animation
5. **Don't animate on every state change**: Only on route changes

## Troubleshooting Guide

### Issue: Double-flash still occurring
**Cause**: AnimatePresence is nested or not wrapping Routes properly
**Solution**: Move AnimatePresence to App.tsx level, wrap Routes directly

### Issue: Animations not triggering
**Cause**: Missing key prop or incorrect location usage
**Solution**: Ensure `key={location.pathname}` on Routes

### Issue: Overlapping animations
**Cause**: Missing `mode="wait"` on AnimatePresence
**Solution**: Add `mode="wait"` to AnimatePresence

### Issue: Performance issues
**Cause**: Complex animations or too many animated elements
**Solution**: Simplify animations, use `will-change: transform` CSS

### Issue: Layout shift during animation
**Cause**: Animated elements affecting document flow
**Solution**: Use transform-based animations only

## Performance Considerations

### Optimization Techniques

1. **Use transform properties**: Avoid animating layout properties
2. **Enable hardware acceleration**: `will-change: transform`
3. **Minimize animated elements**: Only animate the main content area
4. **Use appropriate easing**: 'anticipate' provides smooth feel
5. **Test on mobile**: Ensure 60fps on slower devices

### CSS Optimizations

```css
/* Add to animated elements */
.page-transition {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}
```

## Testing Checklist

- [ ] No content flash on route changes
- [ ] Smooth animations on all routes
- [ ] No layout shifts during transitions
- [ ] Good performance on mobile devices
- [ ] Proper animation sequencing (exit → enter)
- [ ] Static elements remain static
- [ ] Accessibility: respects `prefers-reduced-motion`

## Future Enhancements

### Potential Improvements

1. **Route-specific animations**: Different transitions for different routes
2. **Gesture-based transitions**: Swipe to navigate
3. **Shared element transitions**: Animate elements between pages
4. **Loading states**: Integrate with data fetching
5. **Accessibility**: Respect motion preferences

### Advanced Patterns

```typescript
// Route-specific animations
const getPageVariants = (route: string) => {
  switch (route) {
    case '/contact':
      return slideUpVariants;
    case '/about':
      return fadeVariants;
    default:
      return defaultVariants;
  }
};
```

## Step-by-Step Implementation

### Step 1: Install Dependencies
```bash
npm install framer-motion
# framer-motion should already be installed in this project
```

### Step 2: Update App.tsx
```typescript
// Before: Basic routing
<Routes>
  <Route path="/" element={<Layout />}>
    <Route index element={<Home />} />
  </Route>
</Routes>

// After: With AnimatePresence
<AnimatePresence mode="wait" initial={false}>
  <Routes location={location} key={location.pathname}>
    <Route path="/" element={<Layout />}>
      <Route index element={<Home />} />
    </Route>
  </Routes>
</AnimatePresence>
```

### Step 3: Create PageWrapper Component
```typescript
// src/components/PageWrapper.tsx
import { motion } from 'framer-motion';

const PageWrapper = ({ children }) => (
  <motion.div
    initial="initial"
    animate="in"
    exit="out"
    variants={pageVariants}
    transition={pageTransition}
  >
    {children}
  </motion.div>
);
```

### Step 4: Update Page Components
```typescript
// Before: Direct JSX return
const Home = () => (
  <>
    <Helmet>...</Helmet>
    <div>Page content</div>
  </>
);

// After: Wrapped with PageWrapper
const Home = () => (
  <PageWrapper>
    <Helmet>...</Helmet>
    <div>Page content</div>
  </PageWrapper>
);
```

### Step 5: Clean Up Old Transitions
- Remove old CSS keyframe animations
- Delete unused transition components
- Update any hardcoded delays

## Common Patterns

### Pattern 1: Conditional Animations
```typescript
const variants = {
  initial: (direction) => ({
    x: direction > 0 ? 300 : -300,
    opacity: 0
  }),
  in: { x: 0, opacity: 1 },
  out: (direction) => ({
    x: direction < 0 ? 300 : -300,
    opacity: 0
  })
};
```

### Pattern 2: Staggered Children
```typescript
const containerVariants = {
  in: {
    transition: {
      staggerChildren: 0.1
    }
  }
};

const childVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 }
};
```

## Related Files

- `src/App.tsx` - Main AnimatePresence setup
- `src/components/PageWrapper.tsx` - Reusable animation wrapper
- `src/components/Layout.tsx` - Static layout structure
- `src/pages/*` - All page components using PageWrapper

## Resources

- [Framer Motion AnimatePresence Docs](https://www.framer.com/motion/animate-presence/)
- [React Router Location API](https://reactrouter.com/en/main/hooks/use-location)
- [Web Animation Performance](https://web.dev/animations-guide/)
- [Framer Motion Examples](https://www.framer.com/motion/examples/)

---

*Last updated: January 2025*
*Author: Development Team*
