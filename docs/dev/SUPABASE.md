# Amara Nursing LLC Website Supabase Integration

This document outlines the planned use of Supabase as the Backend-as-a-Service (BaaS) for the Amara Nursing LLC website. Supabase will handle data storage, lead collection, and potentially authentication and serverless functions for dynamic website features.

## Core Objectives for Supabase Usage

1.  **Lead Collection:** Securely capture inquiries from the website's contact forms.
2.  **Dynamic Content Management:** Store and serve content that may change over time (services, bios, blog posts, AFH details) without requiring code deployments for every update.
3.  **Asset Referencing:** Store URLs for images and other assets hosted on external services like Cloudinary.
4.  **Foundation for Future Growth:** Provide a scalable backend for potential future features like patient/caregiver portals or more complex applications.

## Supabase Database Schema & Usage

Below are the proposed tables within the Supabase PostgreSQL database. **Row Level Security (RLS) will be enabled and meticulously configured for all tables, especially those potentially handling sensitive information or intended for specific user access in the future.**

---

### 1. `leads` Table

*   **Purpose:** To store inquiries submitted through the website contact form.
*   **HIPAA Consideration:** This table will contain Personally Identifiable Information (PII) and potentially initial (non-detailed) health-related inquiries. It *must* be treated with extreme care. A **Business Associate Agreement (BAA)** with Supabase is mandatory if this data is considered PHI by Amara Nursing LLC's definition and policies. Access must be strictly limited.
*   **Columns:**
    *   `id` (uuid, primary key, default: `uuid_generate_v4()`)
    *   `created_at` (timestamp with time zone, default: `now()`)
    *   `full_name` (text, not null)
    *   `email` (text, not null)
    *   `phone_number` (text, nullable)
    *   `subject` (text, nullable - e.g., "General Inquiry", "AFH Inquiry", "Service Question")
    *   `message` (text, not null)
    *   `source` (text, default: 'website_contact_form') - To track where the lead came from.
    *   `status` (text, default: 'new') - e.g., 'new', 'contacted', 'resolved'. (For internal tracking by Amara staff)
    *   `ip_address` (inet, nullable) - For security/audit, if deemed necessary and compliant.
    *   `consented_to_contact` (boolean, default: true) - Explicit consent tracking.
*   **RLS Policies:**
    *   **Admin/Staff Write Access:** Only authenticated Amara staff (via a separate admin interface or direct Supabase access with appropriate roles) should be able to read/update `status`.
    *   **Public Insert Access (via Edge Function):** A secure Supabase Edge Function will handle form submissions and insert data. The public should *not* have direct insert access to this table.
    *   **No Public Read Access.**

---

### 2. `services` Table

*   **Purpose:** Store details about the nursing services offered.
*   **Columns:**
    *   `id` (uuid, primary key, default: `uuid_generate_v4()`)
    *   `created_at` (timestamp with time zone, default: `now()`)
    *   `updated_at` (timestamp with time zone, default: `now()`)
    *   `name` (text, not null, unique) - e.g., "Skilled Nursing Care", "Private Duty Nursing (PDN)"
    *   `slug` (text, not null, unique) - For URL generation (e.g., "skilled-nursing-care")
    *   `short_description` (text, nullable) - For overview cards on homepage/services page.
    *   `full_description_markdown` (text, nullable) - Detailed description using Markdown for rich text formatting on the service detail page.
    *   `category` (text, nullable) - e.g., "In-Home", "On-Site", "Delegation"
    *   `icon_identifier` (text, nullable) - Name/ID of an SVG icon to display.
    *   `image_url_cloudinary` (text, nullable) - URL to a representative image on Cloudinary.
    *   `display_order` (integer, default: 0) - For ordering services on the website.
    *   `is_active` (boolean, default: true) - To toggle visibility on the frontend.
*   **RLS Policies:**
    *   **Public Read Access:** Allow anonymous users to read active services.
    *   **Admin/Staff Write Access:** Only authenticated Amara staff should be able to create, update, or delete services.

---

### 3. `founder_bios` Table

*   **Purpose:** Store information about the founders.
*   **Columns:**
    *   `id` (uuid, primary key, default: `uuid_generate_v4()`)
    *   `created_at` (timestamp with time zone, default: `now()`)
    *   `updated_at` (timestamp with time zone, default: `now()`)
    *   `full_name` (text, not null) - e.g., "Diana Mulatya, RN"
    *   `title` (text, not null) - e.g., "Co-founder / Director"
    *   `bio_markdown` (text, not null) - Biography content in Markdown.
    *   `profile_image_url_cloudinary` (text, nullable) - URL to founder's photo on Cloudinary.
    *   `contact_email` (text, nullable) - (If displaying publicly)
    *   `contact_phone` (text, nullable) - (If displaying publicly)
    *   `display_order` (integer, default: 0)
    *   `is_active` (boolean, default: true)
*   **RLS Policies:**
    *   **Public Read Access:** Allow anonymous users to read active bios.
    *   **Admin/Staff Write Access:** Only authenticated Amara staff should be able to manage bios.

---

### 4. `blog_posts` Table

*   **Purpose:** Store content for the website's blog/news section.
*   **Columns:**
    *   `id` (uuid, primary key, default: `uuid_generate_v4()`)
    *   `created_at` (timestamp with time zone, default: `now()`)
    *   `updated_at` (timestamp with time zone, default: `now()`)
    *   `published_at` (timestamp with time zone, nullable) - Date the post goes live.
    *   `title` (text, not null)
    *   `slug` (text, not null, unique) - For URL generation.
    *   `excerpt` (text, nullable) - Short summary for blog listing pages.
    *   `content_markdown` (text, not null) - Main blog content in Markdown.
    *   `author_id` (uuid, nullable, foreign key references `founder_bios(id)` or a future `users` table) - If posts are attributed.
    *   `featured_image_url_cloudinary` (text, nullable) - URL to the post's main image.
    *   `tags` (array of text, nullable) - e.g., `['senior care', 'health tips', 'afh news']`
    *   `status` (text, default: 'draft') - e.g., 'draft', 'published', 'archived'.
    *   `meta_description` (text, nullable) - For SEO.
    *   `meta_keywords` (text, nullable) - For SEO.
*   **RLS Policies:**
    *   **Public Read Access:** Allow anonymous users to read posts where `status` is 'published' and `published_at` is in the past.
    *   **Admin/Staff Write Access:** Only authenticated Amara staff should be able to manage blog posts.

---

### 5. `afh_details` Table (Adult Family Home)

*   **Purpose:** Store specific, dynamic details about the Adult Family Home. While some content is static, certain aspects might benefit from DB storage for easier updates.
*   **Columns:**
    *   `id` (integer, primary key, default: 1) - Likely only one row for the single AFH.
    *   `created_at` (timestamp with time zone, default: `now()`)
    *   `updated_at` (timestamp with time zone, default: `now()`)
    *   `name` (text, default: "Amara Nursing AFH")
    *   `address_line_1` (text, default: "17705 54th Avenue W")
    *   `city` (text, default: "Lynnwood")
    *   `state` (text, default: "WA")
    *   `zip_code` (text, default: "98037")
    *   `main_description_markdown` (text, nullable) - Overview description.
    *   `specializations_markdown` (text, nullable) - List of specializations (e.g., ventilator, parenteral nutrition).
    *   `key_features_json` (jsonb, nullable) - Array of objects for features like `[{ "title": "24/7 RN Staffing", "description": "...", "icon_identifier": "..." }]`.
    *   `gallery_image_urls_cloudinary` (array of text, nullable) - List of Cloudinary URLs for a photo gallery.
    *   `virtual_tour_url` (text, nullable) - Link to a virtual tour if available.
*   **RLS Policies:**
    *   **Public Read Access:** Allow anonymous users to read AFH details.
    *   **Admin/Staff Write Access:** Only authenticated Amara staff should be able to update details.

---

### 6. `site_settings` Table (General Website Configuration)

*   **Purpose:** Store global settings or easily updatable text snippets for the website.
*   **Columns:**
    *   `key` (text, primary key, unique) - e.g., "contact_email", "contact_phone_display", "service_area_zipcodes", "homepage_hero_headline", "branch_office_address"
    *   `value` (text, nullable) - The actual setting value.
    *   `description` (text, nullable) - Explanation of the setting.
    *   `type` (text, default: 'string') - e.g., 'string', 'json_array', 'boolean' (helps admin UI if one is built).
    *   `updated_at` (timestamp with time zone, default: `now()`)
*   **RLS Policies:**
    *   **Public Read Access:** Allow anonymous users to read all settings (as these are public-facing).
    *   **Admin/Staff Write Access:** Only authenticated Amara staff to update.
*   **Example `key` values:**
    *   `service_area_zipcodes`: `["98037", "98101", "98102", ...]` (JSON array stored as text, parsed by frontend)
    *   `default_seo_description`: Text for default meta description.
    *   `testimonials_enabled`: `true` or `false` (boolean as text)

---

### 7. `testimonials` Table (Future Use)

*   **Purpose:** Store client testimonials for display on the website.
*   **Columns:**
    *   `id` (uuid, primary key, default: `uuid_generate_v4()`)
    *   `created_at` (timestamp with time zone, default: `now()`)
    *   `quote` (text, not null)
    *   `author_name` (text, not null) - e.g., "J. Doe", "Family of Patient X"
    *   `author_relation` (text, nullable) - e.g., "Client's Daughter", "Spouse"
    *   `service_received_id` (uuid, nullable, foreign key references `services(id)`) - To associate with a service.
    *   `date_received` (date, nullable)
    *   `is_approved` (boolean, default: false) - Staff must approve before display.
    *   `display_order` (integer, default: 0)
*   **RLS Policies:**
    *   **Public Read Access:** Allow anonymous users to read testimonials where `is_approved` is true.
    *   **Admin/Staff Write Access:** Only authenticated Amara staff to manage and approve.

---

## Supabase Edge Functions

*   **`submit-contact-form` Function:**
    *   **Trigger:** HTTP POST request from the Next.js frontend when the contact form is submitted.
    *   **Action:**
        1.  Validate input data (name, email, message, etc.).
        2.  Perform any necessary sanitization.
        3.  (Optional) Perform a simple spam check (e.g., honeypot, rate limiting).
        4.  Insert the validated data into the `leads` table using the Supabase service role key (securely stored as an environment variable in the Edge Function settings). This bypasses RLS for the insert operation itself but is done within a controlled, secure serverless environment.
        5.  (Optional) Send a notification email to Amara Nursing staff (e.g., using Supabase's integration with an email provider or a third-party email service API called from the function).
        6.  Return a success or error response to the frontend.
    *   **Security:** This function is critical. It must be robust against common web vulnerabilities.

*   **`check-service-area` Function (Optional):**
    *   **Trigger:** HTTP GET request from the frontend with a zip code parameter.
    *   **Action:**
        1.  Query the `site_settings` table (or a dedicated `zip_codes` table) for the provided zip code.
        2.  Return a boolean indicating availability.
    *   **Alternative:** This logic could also live entirely on the frontend if the list of zip codes is small and not frequently updated, fetched from `site_settings` on page load.

## Supabase Storage

*   **Purpose:** While primary image hosting is Cloudinary, Supabase Storage *could* be used for:
    *   Fallback images if Cloudinary integration is delayed.
    *   PDF brochures or documents downloadable from the site (ensure no PHI).
    *   Favicons and other small site assets.
*   **Buckets:**
    *   `public-assets`: For general, publicly accessible files like favicons.
    *   `documents`: For downloadable PDFs (with appropriate RLS if needed, though likely public).
*   **Note:** Cloudinary is generally preferred for image optimization and delivery (CDN). Supabase will primarily store the *URLs* to these Cloudinary assets in the respective tables.

## Admin Interface for Content Management

While Supabase provides a data browser, it's not ideal for non-technical staff (Diana, Faith) to directly manage content. Options:

1.  **Third-Party Headless CMS:** Integrate a user-friendly headless CMS (e.g., Strapi, Sanity, Contentful) that writes to/reads from the Supabase database. This is often the best user experience for content editors. *This would mean the CMS, not Supabase directly, becomes the primary interface for content tables.*
2.  **Custom Admin Panel:** Build a simple, secure admin interface (e.g., using Next.js with Supabase Auth for staff login) that interacts with the Supabase tables. This is more development effort but offers full control.
3.  **Retool/Appsmith/Internal Tool Builder:** Use low-code tools to quickly build an internal admin dashboard that connects to Supabase.

**Recommendation:** For ease of use by Amara staff, a dedicated Headless CMS (Option 1) or a Low-Code Tool (Option 3) is highly recommended over direct Supabase table editing for ongoing content management of services, bios, and blog posts. The `leads` table might be managed via a custom internal tool or email notifications initially.

## Security & Compliance Summary

*   **BAA with Supabase:** Essential if any data managed by Supabase is considered PHI.
*   **Row Level Security (RLS):** Must be implemented on ALL tables.
*   **Secure Edge Functions:** Code for functions handling data input must be secure.
*   **Environment Variables:** Store Supabase keys and other secrets securely.
*   **Regular Audits:** Periodically review RLS policies and access controls.
*   **Data Minimization:** Only store what is necessary.

This Supabase setup provides a flexible and scalable backend for Amara Nursing LLC's website, enabling dynamic content and secure lead capture while minimizing server management overhead.
