# Page Transitions Quick Reference

## TL;DR - What We Did

1. **Moved AnimatePresence to App.tsx** - Wraps Routes, not individual components
2. **Created PageWrapper component** - Reusable animation wrapper for all pages
3. **Updated all page components** - Wrapped content with PageWrapper
4. **Removed old loading screen** - Eliminated 800ms artificial delay
5. **Cleaned up CSS animations** - Removed manual keyframe hacks

## Key Code Snippets

### App.tsx Setup
```typescript
import { AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';

function App() {
  const location = useLocation();
  
  return (
    <AnimatePresence mode="wait" initial={false}>
      <Routes location={location} key={location.pathname}>
        {/* Your routes */}
      </Routes>
    </AnimatePresence>
  );
}
```

### PageWrapper Component
```typescript
import { motion } from 'framer-motion';

const pageVariants = {
  initial: { opacity: 0, y: 20, scale: 0.98 },
  in: { opacity: 1, y: 0, scale: 1 },
  out: { opacity: 0, y: -20, scale: 1.02 },
};

const pageTransition = {
  type: 'tween',
  ease: 'anticipate',
  duration: 0.4,
};

const PageWrapper = ({ children, className = '' }) => (
  <motion.div
    initial="initial"
    animate="in"
    exit="out"
    variants={pageVariants}
    transition={pageTransition}
    className={`w-full ${className}`}
  >
    {children}
  </motion.div>
);
```

### Page Component Usage
```typescript
import PageWrapper from '../components/PageWrapper';

const MyPage = () => (
  <PageWrapper>
    <Helmet>...</Helmet>
    {/* Your page content */}
  </PageWrapper>
);
```

## Critical Props Explained

| Prop | Purpose | Why Important |
|------|---------|---------------|
| `mode="wait"` | Exit completes before enter starts | Prevents double-flash |
| `initial={false}` | No animation on first load | Better UX |
| `key={location.pathname}` | Triggers animation on route change | Essential for detection |
| `location={location}` | Passes location to Routes | Required for key to work |

## Animation Breakdown

```typescript
// Entry: Page slides up and fades in
initial: { opacity: 0, y: 20, scale: 0.98 }

// Active: Page is fully visible
in: { opacity: 1, y: 0, scale: 1 }

// Exit: Page slides up and fades out
out: { opacity: 0, y: -20, scale: 1.02 }
```

## Common Issues & Fixes

| Issue | Cause | Fix |
|-------|-------|-----|
| Double-flash | AnimatePresence in wrong place | Move to App.tsx, wrap Routes |
| No animation | Missing key prop | Add `key={location.pathname}` |
| Overlapping | Missing mode="wait" | Add `mode="wait"` to AnimatePresence |
| Performance | Complex animations | Use transform-only properties |

## File Structure
```
src/
├── App.tsx                    # AnimatePresence setup
├── components/
│   ├── Layout.tsx            # Static layout (no animation)
│   └── PageWrapper.tsx       # Animation wrapper
└── pages/
    ├── Home.tsx              # Wrapped with PageWrapper
    ├── About.tsx             # Wrapped with PageWrapper
    └── ...                   # All pages wrapped
```

## Testing Checklist

- [ ] No content flash between routes
- [ ] Smooth 400ms transitions
- [ ] Header/Footer stay static
- [ ] Works on mobile devices
- [ ] No console errors
- [ ] Good performance (60fps)

## Performance Tips

1. **Use transform properties only** - Avoid animating width, height, etc.
2. **Add will-change CSS** - `will-change: transform, opacity`
3. **Test on slower devices** - Ensure smooth on mobile
4. **Keep duration short** - 300-500ms max
5. **Minimize animated elements** - Only animate main content

## Customization Examples

### Different animation per route:
```typescript
const getVariants = (pathname) => {
  if (pathname === '/contact') return slideUpVariants;
  if (pathname === '/about') return fadeVariants;
  return defaultVariants;
};
```

### Conditional animations:
```typescript
const variants = {
  initial: { opacity: 0, x: isForward ? 100 : -100 },
  in: { opacity: 1, x: 0 },
  out: { opacity: 0, x: isForward ? -100 : 100 },
};
```

## Need Help?

1. Check the full guide: `docs/dev/PAGE_TRANSITIONS_GUIDE.md`
2. Review Framer Motion docs: [framer.com/motion](https://www.framer.com/motion/)
3. Test in browser dev tools with slow 3G throttling
4. Use React DevTools to debug component mounting

---

*Quick reference for the Amara Nursing website page transitions*
