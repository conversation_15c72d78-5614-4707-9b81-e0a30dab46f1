# Development Documentation

This folder contains technical documentation for developers working on the Amara Nursing website.

## 📚 Documentation Index

### 🎬 Page Transitions & Animations
- **[PAGE_TRANSITIONS_GUIDE.md](./PAGE_TRANSITIONS_GUIDE.md)** - Complete guide to our React Router + Framer Motion implementation
- **[PAGE_TRANSITIONS_QUICK_REFERENCE.md](./PAGE_TRANSITIONS_QUICK_REFERENCE.md)** - Quick reference for developers
- **[PAGE_TRANSITIONS_TROUBLESHOOTING.md](./PAGE_TRANSITIONS_TROUBLESHOOTING.md)** - Common issues and solutions

### 🔧 Core Implementation
- **[react-implementation.md](./react-implementation.md)** - React frontend setup and patterns
- **[contact-form-setup.md](./contact-form-setup.md)** - Contact form implementation
- **[SUPABASE.md](./SUPABASE.md)** - Database and backend setup

### 🔍 SEO & Performance
- **[SEO_STRATEGY.md](./SEO_STRATEGY.md)** - SEO implementation strategy
- **[SEO_QUICK_REFERENCE.md](./SEO_QUICK_REFERENCE.md)** - SEO quick reference

### 🔒 Security & Deployment
- **[security-deployment.md](./security-deployment.md)** - Security best practices and deployment

## 🚀 Quick Start for New Developers

### 1. Understanding Page Transitions
Our website uses smooth page transitions powered by Framer Motion. Start here:
1. Read [PAGE_TRANSITIONS_QUICK_REFERENCE.md](./PAGE_TRANSITIONS_QUICK_REFERENCE.md)
2. Review the implementation in `src/App.tsx` and `src/components/PageWrapper.tsx`
3. If you encounter issues, check [PAGE_TRANSITIONS_TROUBLESHOOTING.md](./PAGE_TRANSITIONS_TROUBLESHOOTING.md)

### 2. Development Environment Setup
```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build
```

### 3. Key Technologies
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Framer Motion** for animations
- **React Router** for navigation
- **React Hook Form** for forms
- **Supabase** for backend (if applicable)

## 📋 Development Guidelines

### Code Style
- Use TypeScript for all new components
- Follow React functional component patterns
- Use Tailwind CSS classes for styling
- Implement proper error boundaries
- Add proper TypeScript types

### Animation Guidelines
- Use Framer Motion for all animations
- Keep transitions under 500ms
- Test on mobile devices
- Respect `prefers-reduced-motion`
- Use transform-based animations only

### Performance Best Practices
- Optimize images and assets
- Use lazy loading for routes
- Minimize bundle size
- Test on slow networks
- Monitor Core Web Vitals

## 🛠️ Common Development Tasks

### Adding a New Page
1. Create page component in `src/pages/`
2. Wrap content with `PageWrapper` component
3. Add route to `src/App.tsx`
4. Update navigation if needed
5. Test page transitions

### Modifying Animations
1. Edit `src/components/PageWrapper.tsx`
2. Adjust `pageVariants` and `pageTransition`
3. Test across all routes
4. Verify performance on mobile

### Updating Styles
1. Use Tailwind CSS classes when possible
2. Add custom CSS to `src/index.css` if needed
3. Follow existing color scheme and spacing
4. Test responsive design

### Debugging Issues
1. Check browser console for errors
2. Use React DevTools for component debugging
3. Test with network throttling
4. Verify animations with reduced motion settings

## 📱 Testing Checklist

### Functionality
- [ ] All routes work correctly
- [ ] Forms submit properly
- [ ] Navigation is intuitive
- [ ] Contact information is accurate

### Performance
- [ ] Page load times under 3 seconds
- [ ] Smooth animations on mobile
- [ ] No layout shifts
- [ ] Good Lighthouse scores

### Accessibility
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Proper heading hierarchy
- [ ] Color contrast compliance
- [ ] Reduced motion support

### Cross-Browser
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

## 🆘 Getting Help

### Internal Resources
1. Check relevant documentation in this folder
2. Review existing code patterns
3. Test in development environment
4. Use browser developer tools

### External Resources
- [React Documentation](https://react.dev/)
- [Framer Motion Docs](https://www.framer.com/motion/)
- [Tailwind CSS Docs](https://tailwindcss.com/)
- [Vite Documentation](https://vitejs.dev/)

### Emergency Contacts
- For critical issues affecting production
- For security vulnerabilities
- For deployment problems

## 📝 Contributing

### Before Making Changes
1. Read relevant documentation
2. Test changes locally
3. Check for TypeScript errors
4. Verify animations work properly
5. Test on mobile devices

### Code Review Process
1. Create feature branch
2. Make changes with proper commits
3. Test thoroughly
4. Submit for review
5. Address feedback
6. Merge when approved

---

*This documentation is maintained by the development team. Last updated: January 2025*
