# Deployment Guide

## Netlify Deployment

This project is optimized for deployment on Netlify with the following configuration:

### Prerequisites
- Node.js 18 (specified in `.nvmrc`)
- npm with `--legacy-peer-deps` flag

### Automatic Deployment Setup

1. **Connect Repository to Netlify**
   - Go to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository: `https://github.com/amaranursing/amarawebsite.git`
   - Select the `master` branch for production deployments

2. **Build Settings** (Auto-detected from `netlify.toml`)
   - **Build command**: `node resolutions.js && npm install --legacy-peer-deps && npm run build`
   - **Publish directory**: `dist`
   - **Node version**: 18

3. **Environment Variables** (if needed)
   - Set any required environment variables in Netlify UI
   - Currently no environment variables are required for basic functionality

### Manual Deployment

If you prefer to deploy manually:

```bash
# Install dependencies
npm install --legacy-peer-deps

# Build the project
npm run build

# The dist/ folder contains the built files ready for deployment
```

### Configuration Files

- **`netlify.toml`**: Main Netlify configuration
- **`public/_redirects`**: Backup redirect rules for SPA routing
- **`.nvmrc`**: Node.js version specification

### Build Optimizations

- TypeScript compilation with error tolerance
- Vite bundling with tree-shaking
- Asset optimization and caching headers
- Security headers included
- Client-side routing support

### Troubleshooting

**Common Issues:**

1. **Build fails with TypeScript errors**
   - The build is configured with `--noEmitOnError false` to allow builds with minor TS issues
   - Check the build logs for specific errors

2. **React version conflicts**
   - The project uses React resolution overrides in `package.json`
   - Use `--legacy-peer-deps` flag for npm install

3. **Routing issues**
   - Client-side routing is configured via redirects
   - All routes redirect to `/index.html` with 200 status

4. **Asset loading issues**
   - All assets are served from the `public/` directory
   - Check that image paths are correct (e.g., `/images/hero-image.jpg`)

### Performance Features

- Immutable caching for static assets (1 year)
- Gzip compression enabled
- Optimized bundle splitting
- Image optimization headers

### Security Features

- CSP headers configured for Google Maps integration
- XSS protection enabled
- Frame options set to DENY
- Referrer policy configured

## Alternative Deployment Options

### Vercel
- Import the repository in Vercel
- Set build command: `npm run build`
- Set output directory: `dist`
- Set Node.js version: 18

### Other Static Hosts
- Build the project locally: `npm run build`
- Upload the `dist/` folder contents to your hosting provider
- Configure redirects for SPA routing (all routes → `/index.html`)
