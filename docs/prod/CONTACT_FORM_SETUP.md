# Contact Form Setup Guide

## Overview

The contact form has been implemented using **Netlify Functions** with **Resend** for email delivery. This approach provides:

- ✅ **Security**: API keys are kept server-side
- ✅ **Reliability**: Server-side validation and spam protection
- ✅ **Simplicity**: No database required (as per client requirements)
- ✅ **Cost-effective**: Uses existing Netlify infrastructure

## Architecture

```
User fills form → React Component → Netlify Function → Resend API → Emails sent
```

## Setup Instructions

### 1. Environment Variables

Set these environment variables in your Netlify dashboard:

```bash
RESEND_API_KEY=re_G4Q2Yn4K_CTzZwa1P7BopmNy4SvttUPQE
```

**How to set in Netlify:**
1. Go to your Netlify site dashboard
2. Navigate to **Site settings** → **Environment variables**
3. Click **Add variable**
4. Add the `RESEND_API_KEY` with the provided value

### 2. Resend Configuration

The system is configured to use:
- **API Key**: `re_G4Q2Yn4K_CTzZwa1P7BopmNy4SvttUPQE`
- **From Email**: `<EMAIL>` (verified domain)
- **To Email**: `<EMAIL>` (as requested)

**Important**: Using `<EMAIL>` because `amaracares.com` domain is not verified in Resend. To use your own domain, you need to verify it in the Resend dashboard.

### 3. Email Flow

When a user submits the contact form:

1. **Business Notification Email** → `<EMAIL>`
   - Contains all form details
   - Includes customer's email as reply-to
   - Professional HTML formatting

2. **Customer Confirmation Email** → Customer's email
   - Thanks them for contacting
   - Confirms message received
   - Provides contact phone numbers
   - Professional branding

### 4. Spam Protection

The function includes basic spam detection:
- URL detection in messages
- Minimum name/message length requirements
- Keyword filtering (viagra, casino, loan, etc.)
- Input sanitization to prevent XSS

### 5. Form Validation

Client-side validation (React):
- Required fields: Full Name, Email, Subject, Message
- Email format validation
- Message length limits (10-2000 characters)
- Real-time error display

Server-side validation (Netlify Function):
- Duplicate validation for security
- Input sanitization
- Spam score calculation

## Testing

### Local Testing

1. Install dependencies:
```bash
npm install
```

2. Test the function locally:
```bash
node test-contact-form.js
```

### Production Testing

1. Deploy to Netlify
2. Fill out the contact form on the live site
3. Check that emails are received at `<EMAIL>`
4. Verify customer receives confirmation email

## File Structure

```
├── netlify/
│   └── functions/
│       └── contact-form.js          # Main function handler
├── src/
│   └── components/
│       └── ContactForm.tsx          # Updated React component
├── .env.example                     # Environment variables template
├── test-contact-form.js            # Test script
└── CONTACT_FORM_SETUP.md           # This file
```

## Deployment

The contact form will automatically deploy with your Netlify site. The implementation includes:

1. **Netlify Function**: `netlify/functions/contact-form.js` (ES modules)
2. **React Component**: Updated `src/components/ContactForm.tsx`
3. **Configuration**: Updated `netlify.toml` with functions directory
4. **Dependencies**: Added `resend` package to main project

### Deployment Steps:
1. Set `RESEND_API_KEY` environment variable in Netlify
2. Push code to your repository
3. Netlify will automatically build and deploy
4. Test the contact form on the live site

## Monitoring

Check Netlify function logs for:
- Successful submissions
- Error messages
- Spam detection alerts

## Troubleshooting

### Common Issues

1. **"Function not found" error (404)**
   - Check URL: `https://amaracares.netlify.app/.netlify/functions/contact-form`
   - Ensure `netlify.toml` includes `functions = "netlify/functions"`
   - Verify function file is at `netlify/functions/contact-form.js`
   - Check Netlify deploy logs for function deployment

2. **Resend 403 errors**
   - ✅ **FIXED**: Now using `<EMAIL>` (verified domain)
   - If you want to use your own domain, verify `amaracares.com` in Resend dashboard
   - Check API key is correct in Netlify environment variables

3. **Function shows success but no emails received**
   - Check Netlify function logs for errors
   - Verify `RESEND_API_KEY` environment variable is set
   - Check spam folders in Gmail
   - Look at Resend dashboard for delivery status

4. **CORS errors**
   - Function includes proper CORS headers
   - Should work from any domain

### Debugging Steps

1. **Check Function Deployment**:
   - Go to Netlify dashboard → Functions tab
   - Verify `contact-form` function is listed
   - Check function logs for errors

2. **Test Function Directly**:
   ```bash
   curl -X POST https://amaracares.netlify.app/.netlify/functions/contact-form \
     -H "Content-Type: application/json" \
     -d '{"fullName":"Test User","email":"<EMAIL>","subject":"test","message":"Test message"}'
   ```

3. **Check Environment Variables**:
   - Netlify dashboard → Site settings → Environment variables
   - Ensure `RESEND_API_KEY` is set correctly

### Support

For issues with:
- **Resend API**: Check Resend dashboard and documentation
- **Netlify Functions**: Check Netlify function logs
- **Form validation**: Check browser console for errors

## Security Notes

- All inputs are sanitized server-side
- Spam detection prevents abuse
- No sensitive data is logged
- CORS properly configured
- Rate limiting can be added if needed

## Future Enhancements

If budget allows in the future:
- Database storage for form submissions
- Advanced spam detection
- Email templates with better branding
- Analytics and reporting
- Automated follow-up sequences
