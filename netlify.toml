[build]
  publish = "dist"
  command = "node resolutions.js && npm install --legacy-peer-deps && npm run build"
  functions = "netlify/functions"

# Environment variables (defaults, can be overridden in Netlify UI)
[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--legacy-peer-deps"
  CI = "false"
  # Skip TypeScript errors during build
  TSC_COMPILE_ON_ERROR = "true"

# Redirects for client-side routing (React Router)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Cache control for static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Cache control for images
[[headers]]
  for = "/*.png"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"
    
[[headers]]
  for = "/*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.jpeg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.ico"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "/*.svg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' https://maps.googleapis.com; img-src 'self' data: https://*.googleapis.com https://*.gstatic.com; font-src 'self' data:; connect-src 'self'; frame-src https://www.google.com;"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(self)"
