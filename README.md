# Amara Nursing LLC - Website

## Project Overview

This project is the official website for Amara Nursing LLC, a provider of in-home and on-site nursing services, including an Adult Family Home (AFH). The website aims to establish Amara Nursing LLC as a trusted, state-compliant provider, clearly showcase core offerings, ensure seamless client access, highlight RN leadership, and prioritize HIPAA compliance and a mobile-friendly design.

The primary audience includes families seeking care, post-hospitalization patients, caregivers/group homes, chronic illness patients, and referring medical professionals.

## Design & Branding

*   **Core Message:** "Skilled Nursing, Delivered with Heart" (or similar, to be finalized based on client's core message from questionnaire).
*   **Brand Voice:** Professional yet empathetic, authoritative but approachable.
*   **Design Style:** Modern, minimalist, warm, inviting, and professional. The design prioritizes clarity, trust, compassion, and accessibility.
*   **Color Palette (Primary Focus):**
    *   Dark Teal: `#135673`
    *   Muted Blue-Gray: `#6a92a1`
    *   Light Blue: `#bad9e4`
    *   Neutrals: Whites, Off-Whites, Light Grays.
    *   *Note: The logo pink (`#ee2e74`) will be used minimally, primarily within the logo itself, as per client preference to avoid it in the main UI for a "warm inviting" feel.*
*   **Typography:** Legible sans-serif fonts suitable for accessibility (e.g., Open Sans, Lato, Roboto).

## Key Features

*   **Services Showcase:** Detailed descriptions of In-Home Nursing Services (Skilled Nursing Care, Private Duty Nursing, Post-Hospitalization Recovery) and On-Site Nursing Services (Adult Family Homes, Nurse Delegation).
*   **Founder Bios:** Professional profiles for Diana Mulatya, RN, and Faith Mutuku, RN.
*   **HIPAA-Compliant Contact Forms:** Secure way for potential clients to make inquiries.
*   **Service Area Checker:** Allows users to verify if their location (Seattle + zip codes) is within Amara Nursing's service area.
*   **Mobile-Responsive Design:** Optimized for all devices (desktops, tablets, smartphones).
*   **Accessibility Focused:** Designed and developed with WCAG guidelines in mind for users with disabilities, including elderly individuals.
*   **Future Potential:** Testimonials section, Blog/news section, Online appointment requests, Patient/caregiver portals.

## Technical Stack

*   **Frontend:** React with TypeScript
    *   **Build Tool:** Vite for fast development and optimized builds
    *   **Routing:** React Router for client-side navigation
*   **Styling:** Tailwind CSS
    *   *Reasoning:* Utility-first for rapid development of a modern, consistent UI.
*   **UI Components:** Framer Motion for animations, Lucide React for icons
*   **Forms:** React Hook Form for efficient form handling
*   **Hosting:** Vercel (recommended) or other static hosting providers
*   **Domain:** `amaracares.com`

## HIPAA Compliance Considerations (CRITICAL)

This website will handle potentially sensitive information. If any Protected Health Information (PHI) is collected, processed, or stored (e.g., through contact forms intended for patient inquiries, or future patient portals):

1.  **Business Associate Agreement (BAA):** A signed BAA *must* be in place with Supabase (and any other relevant third-party service handling PHI) before any PHI is handled by the platform.
2.  **Shared Responsibility:**
    *   **Supabase:** Provides secure infrastructure.
    *   **Developer/Amara Nursing LLC:** Responsible for secure application design, configuration (especially Row Level Security in Supabase), secure coding practices, access controls, and adherence to Amara Nursing LLC's internal HIPAA policies.
3.  **Secure Data Handling:**
    *   All PHI must be encrypted in transit (TLS/SSL) and at rest.
    *   Strict Row Level Security (RLS) policies must be implemented in Supabase for any tables containing PHI.
    *   Edge Functions handling PHI must be developed securely.
    *   Data minimization principles will be followed.

## Getting Started

### Prerequisites

*   Node.js (version 18 or later)
*   npm or yarn
*   Git for version control

### Environment Variables

Currently, no environment variables are required for basic functionality. If you need to add API keys or other configuration in the future, create a `.env.local` file in the root directory.

### Installation

1.  Clone the repository:
    ```bash
    git clone https://github.com/amaranursing/amarawebsite.git
    cd amarawebsite
    ```
2.  Install dependencies:
    ```bash
    npm install --legacy-peer-deps
    ```

### Running Locally

```bash
npm run dev
```
Open [http://localhost:5173](http://localhost:5173) in your browser (Vite default port).

### Building for Production

```bash
npm run build
```

### Deployment

The project is optimized for deployment on Vercel or other static hosting providers. Simply connect your Git repository to your hosting provider and it will automatically detect the Vite configuration.

## Project Structure

```
/public
  /images          # Website images and assets
  /logos           # Logo files
  favicon.ico
  robots.txt
  sitemap.xml
/src
  /components
    /common        # Reusable UI components
    /layout        # Header, Footer, Navigation
    /forms         # Contact forms
    /ui            # UI components
  /pages           # Page components (Home, About, Services, etc.)
  /data            # Static data and content
  App.tsx          # Main app component
  main.tsx         # App entry point
  index.css        # Global styles
# Configuration files
package.json
vite.config.ts
tailwind.config.js
tsconfig.json
```

## Future Development / To-Do

*   Implement Testimonials section.
*   Develop Blog/News section.
*   Integrate Online Appointment Request functionality (requires careful HIPAA planning).
*   Develop Patient/Caregiver Portals (major feature, requires extensive HIPAA planning and BAA).
*   Set up ongoing SEO monitoring and optimization.
*   Content updates as requested by the client.
*   Develop the "app to log in/out of shift" and "upload pt information" (separate major project).

## Contact

*   **Client:** Amara Nursing LLC
*   **Development:** <EMAIL>

## Git Workflow

This project follows the **Gitflow branching model**:

- **`master`** - Production-ready code (stable releases)
- **`develop`** - Integration branch for features (active development)

### Branching Strategy

1. **Feature development**: Create feature branches from `develop`
2. **Integration**: Merge completed features into `develop`
3. **Releases**: Merge `develop` into `master` for production releases

For more details, see: [A successful Git branching model](https://nvie.com/posts/a-successful-git-branching-model/)

## Deployment

### Vercel (Recommended)

1. Push this repository to GitHub, GitLab, or Bitbucket
2. Create a new project in Vercel and connect it to your repository
3. Set the production branch to `master`
4. Vercel will automatically detect the Vite configuration
5. The site will be built and deployed automatically

### Other Static Hosting Providers

This project can be deployed to any static hosting provider that supports single-page applications:

- Netlify
- GitHub Pages
- Firebase Hosting
- AWS S3 + CloudFront

Make sure to configure redirects for client-side routing (all routes should redirect to `/index.html`).
