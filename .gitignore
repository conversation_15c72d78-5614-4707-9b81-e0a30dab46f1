# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/
/build
.next

# Production
/build
/dist

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env*.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# macOS system files
.DS_Store
.AppleDouble
.LSOverride
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# IDE/Editor folders
.idea/
.vscode/
*.swp
*.swo
.project
.classpath
.settings/

# Design Documentation
/docs/design/
/docs/design/**/*

# Misc
*.log
*.pid
*.seed
*.pid.lock
